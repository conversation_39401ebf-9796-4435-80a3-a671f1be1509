import NextAuth from 'next-auth';
import Google from 'next-auth/providers/google';
import Credentials from 'next-auth/providers/credentials';
import { getOrGenerateDeviceId } from '@/utils/deviceId';
import { AuthLoginResultI } from '@/networks/auth/types';

export type AuthLoginParamsI = {
  type: 'GOOGLE' | 'EMAIL_PASSWORD';
  email?: string;
  password?: string;
  externalToken?: string;
  deviceToken?: string;
  ip?: string;
  versionNo: string;
  deviceId: string;
  platform: string;
};

export async function callBackendLogin(params: any): Promise<any> {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL}/backend/api/v1/auth/login`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-version-no': params.versionNo,
        'x-device-id': params.deviceId,
        'x-platform': params.platform,
        'x-api-key': process.env.NEXT_PUBLIC_API_KEY || '',
      },
      body: JSON.stringify({
        type: params.type,
        email: params.email,
        password: params.password,
        externalToken: params.externalToken,
        deviceToken: params.deviceToken,
      }),
    }
  );

  const data = await response.json();
  if (!data.token) {
    throw new Error(`Backend login failed: ${response.statusText}`);
  }

  return data;
}

export const { handlers, signIn, signOut, auth } = NextAuth({
  pages: {
    signIn: '/login',
  },
  providers: [
    Google,
    Credentials({
      credentials: {
        email: {
          type: 'email',
          label: 'Email',
          placeholder: '<EMAIL>',
        },
        password: {
          type: 'password',
          label: 'Password',
          placeholder: '*****',
        },
      },

      authorize: async credentials => {
        try {
          const deviceId = getOrGenerateDeviceId();

          const loginParams: AuthLoginParamsI = {
            type: 'EMAIL_PASSWORD',
            email: credentials.email as string,
            password: credentials.password as string,
            versionNo: process.env.APP_VERSION || '1.0.0',
            deviceId,
            platform: 'web_app',
          };

          const loginResult = await callBackendLogin(loginParams);

          // Fetch profile details and combine with login result
          try {
            const fetchProfileDetails = (profileId?: string) => {
              return fetch(
                `${process.env.NEXT_PUBLIC_BASE_URL}/backend/api/v1/user/profile/data/${profileId}`,
                {
                  method: 'GET',
                  headers: {
                    'Content-Type': 'application/json',
                    'x-version-no': process.env.APP_VERSION || '1.0.0',
                    'x-device-id': getOrGenerateDeviceId(),
                    'x-platform': 'web_app',
                    'x-api-key': process.env.NEXT_PUBLIC_API_KEY || '',
                    Authorization: `Bearer ${loginResult.token}`,
                  },
                }
              );
            };

            const profileData = await fetchProfileDetails(
              loginResult.profileId
            );
            const profileJson = await profileData.json();

            // Combine login result with profile data
            const combinedData: AuthLoginResultI = {
              ...loginResult,
              designation: profileJson.designation || null,
              entity: profileJson.entity || null,
            };

            return {
              id: loginResult.profileId,
              email: loginResult.email,
              name: loginResult.name,
              image: loginResult.avatar,
              backendData: combinedData,
              deviceId,
            };
          } catch (err) {
            console.log(err);
            return {
              id: loginResult.profileId,
              email: loginResult.email,
              name: loginResult.name,
              image: loginResult.avatar,
              backendData: loginResult,
              deviceId,
            };
          }
        } catch (error) {
          console.error('Login error:', error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === 'credentials') {
      }

      if (account?.provider === 'google') {
        try {
          const deviceId = getOrGenerateDeviceId();

          const loginParams: AuthLoginParamsI = {
            type: 'GOOGLE',
            email: user.email!,
            externalToken: account.id_token!,
            versionNo: process.env.APP_VERSION || '1.0.0',
            deviceId,
            platform: process.env.PLATFORM || 'web_app',
          };

          const loginResult = await callBackendLogin(loginParams);

          // Fetch profile details and combine with login result
          try {
            const fetchProfileDetails = (profileId?: string) => {
              return fetch(
                `${process.env.NEXT_PUBLIC_BASE_URL}/backend/api/v1/user/profile/data/${profileId}`,
                {
                  method: 'GET',
                  headers: {
                    'Content-Type': 'application/json',
                    'x-version-no': process.env.APP_VERSION || '1.0.0',
                    'x-device-id': deviceId,
                    'x-platform': 'web_app',
                    'x-api-key': process.env.NEXT_PUBLIC_API_KEY || '',
                    Authorization: `Bearer ${loginResult.token}`,
                  },
                }
              );
            };

            const profileData = await fetchProfileDetails(
              loginResult.profileId
            );
            const profileJson = await profileData.json();

            // Combine login result with profile data
            const combinedData: AuthLoginResultI = {
              ...loginResult,
              // Add fields from profile API that are not in login API
              designation: profileJson.designation || null,
              entity: profileJson.entity || null,
            };

            // Store combined backend data in user object
            user.backendData = combinedData;
            user.deviceId = deviceId;
            user.id = loginResult.profileId;
          } catch (err) {
            console.log(
              err,
              'errorChecking profile fetch in Google login.....'
            );
            // If profile fetch fails, still use login data
            user.backendData = loginResult;
            user.deviceId = deviceId;
            user.id = loginResult.profileId;
          }

          return true;
        } catch (error) {
          console.error('Google login backend error:', error);
          return false;
        }
      }

      return true;
    },
    async jwt({ token, user }) {
      if (user?.backendData) {
        token.backendData = user.backendData;
        token.deviceId = user.deviceId;
        token.backendJwtToken = user.backendData.jwtToken;
      }
      return token;
    },
    async session({ session, token }) {
      if (token.backendData) {
        const backendData = token.backendData as AuthLoginResultI;
        session.user.backendData = backendData;
        session.user.deviceId = token.deviceId as string;
        session.user.backendJwtToken = token.backendJwtToken as string;
        session.user.id = backendData.profileId;

        // Also add the combined data to session root for backward compatibility
        (session as any).profileId = backendData.profileId;
        (session as any).username = backendData.username;
        (session as any).designationText = backendData.designationText;
        (session as any).entityText = backendData.entityText;
        (session as any).avatar = backendData.avatar;
        (session as any).isEmailVerified = backendData.isEmailVerified;
        (session as any).isUsernameSaved = backendData.isUsernameSaved;
        (session as any).isPersonalDetailsSaved =
          backendData.isPersonalDetailsSaved;
        (session as any).isWorkDetailsSaved = backendData.isWorkDetailsSaved;
        (session as any).isPrivacyPolicyAccepted =
          backendData.isPrivacyPolicyAccepted;
        (session as any).previousStatus = backendData.previousStatus;
        (session as any).designation = backendData.designation;
        (session as any).entity = backendData.entity;
        (session as any).token = backendData.token;
        (session as any).jwtToken = backendData.jwtToken;
        (session as any).deviceId = token.deviceId;
      }

      return session;
    },
  },
  session: {
    strategy: 'jwt',
    maxAge: 60 * 24 * 60 * 60,
  },
});
