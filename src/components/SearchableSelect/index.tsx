'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

export type SearchableOptionI = {
  id: string;
  name: string;
  dataType: string;
};

export type SearchableSelectPropsI = {
  value?: SearchableOptionI | null;
  placeholder?: string;
  onValueChange?: (value: SearchableOptionI | null) => void;
  onSearch?: (query: string) => Promise<SearchableOptionI[]>;
  disabled?: boolean;
  error?: string;
  className?: string;
  label?: string;
  loading?: boolean;
};

const SearchableSelect = React.forwardRef<
  HTMLInputElement,
  SearchableSelectPropsI
>(
  (
    {
      value,
      placeholder = 'Search and select...',
      onValueChange,
      onSearch,
      disabled,
      error,
      className = '',
      label,
      loading,
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [options, setOptions] = useState<SearchableOptionI[]>([]);
    const [isSearching, setIsSearching] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          dropdownRef.current &&
          !dropdownRef.current.contains(event.target as Node)
        ) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    useEffect(() => {
      if (searchQuery && onSearch) {
        const timeoutId = setTimeout(async () => {
          setIsSearching(true);
          try {
            const results = await onSearch(searchQuery);
            setOptions(results);
          } catch (error) {
            console.error('Search error:', error);
            setOptions([]);
          } finally {
            setIsSearching(false);
          }
        }, 300);

        return () => clearTimeout(timeoutId);
      } else {
        setOptions([]);
      }
    }, [searchQuery, onSearch]);

    const handleSelect = (option: SearchableOptionI) => {
      onValueChange?.(option);
      setSearchQuery(option.name);
      setIsOpen(false);
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const query = e.target.value;
      setSearchQuery(query);
      setIsOpen(true);

      if (!query) {
        onValueChange?.(null);
        setOptions([]);
      }
    };

    const displayValue = value ? value.name : searchQuery;

    return (
      <div className={`relative ${className}`}>
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {label}
          </label>
        )}
        <div className="relative" ref={dropdownRef}>
          <input
            ref={ref}
            type="text"
            className={`
              w-full rounded-md border bg-white py-2 pl-3 pr-10 text-left shadow-sm 
              focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary sm:text-sm
              ${error ? 'border-red-300' : 'border-gray-300'}
              ${disabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : 'hover:border-gray-400'}
            `}
            placeholder={placeholder}
            value={displayValue}
            onChange={handleInputChange}
            onFocus={() => setIsOpen(true)}
            disabled={disabled || loading}
          />
          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
            {isSearching ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            ) : (
              <ChevronDownIcon
                className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${
                  isOpen ? 'rotate-180' : ''
                }`}
                aria-hidden="true"
              />
            )}
          </span>

          {isOpen && options.length > 0 && (
            <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
              {options.map(option => (
                <div
                  key={`${option.id}-${option.dataType}`}
                  className="relative cursor-pointer select-none py-2 pl-3 pr-9 text-gray-900 hover:bg-primary hover:text-white"
                  onClick={() => handleSelect(option)}
                >
                  <span className="block truncate font-normal">
                    {option.name}
                  </span>
                  {option.dataType && (
                    <span className="text-xs opacity-75">
                      {option.dataType}
                    </span>
                  )}
                </div>
              ))}
            </div>
          )}

          {isOpen && searchQuery && options.length === 0 && !isSearching && (
            <div className="absolute z-10 mt-1 w-full rounded-md bg-white py-2 text-base shadow-lg ring-1 ring-black ring-opacity-5 sm:text-sm">
              <div className="px-3 py-2 text-gray-500 text-center">
                No results found
              </div>
            </div>
          )}
        </div>
        {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
      </div>
    );
  }
);

SearchableSelect.displayName = 'SearchableSelect';

export { SearchableSelect };
