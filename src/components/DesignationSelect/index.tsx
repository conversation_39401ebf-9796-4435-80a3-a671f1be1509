'use client';

import React from 'react';
import { ApiSelect } from '@/components';
import { DesignationSelectPropsI } from './types';
import { ApiSelectParamsI, ApiSelectResponseI } from '../ApiSelect/types';
import { apiCall } from '@/lib/api';

const fetchDesignations = async (params: ApiSelectParamsI): Promise<ApiSelectResponseI> => {
  if (params.search === '' || params.search === undefined) {
    params.search = 'a';
  }
  const response = await apiCall(
    '/backend/api/v1/company/designation/options',
    'GET',
    {
      isAuth: true,
      query: { ...params, page: String(params.page) },
    }
  );
  console.log(response, 'responseOfDesignations');
  return response as ApiSelectResponseI;
};

const DesignationSelect = ({
  control,
  name,
  label = 'Designation',
  placeholder = 'Select your designation',
  isRequired = false,
  disabled = false,
  className = 'w-full',
  onChange,
}: DesignationSelectPropsI) => {
  return (
    <ApiSelect
      control={control}
      name={name}
      label={label}
      placeholder={placeholder}
      isRequired={isRequired}
      disabled={disabled}
      className={className}
      searchPlaceholder="Search designations..."
      apiCall={fetchDesignations}
      onChangeOption={onChange}
      optionLabelKey="name"
      optionValueKey="id"
      uniqueKey="id"
      requiredMessage="Please select a designation"
    />
  );
};

export default DesignationSelect;
