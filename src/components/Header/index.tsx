'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import Logo from '../Logo';
import { CONFIG } from '../../constants/config';
import ForumIcon from '@assets/svg/Forum';

const Header = () => {
  const pathname = usePathname();
  const isForumsActive = pathname === '/' || pathname === '/forums';

  return (
    <header
      className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm"
      style={{ height: CONFIG.layout.headerHeight }}
    >
      <div className="max-w-screen-xl mx-auto px-4 h-full">
        <div className="flex items-center justify-between h-full">
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <Logo compact={false} />
            </Link>
          </div>

          <div className="flex items-center space-x-2">
            {/* Forums Navigation */}
            <Link
              href="/"
              className={`flex items-center px-4 py-2 rounded-lg transition-colors duration-200 ${
                isForumsActive
                  ? 'bg-[#448600] text-white'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              <ForumIcon
                size={20}
                className="mr-2"
              />
              <span className="text-sm font-medium">Forums</span>
            </Link>

            {/* Home Icon (for mobile) */}
            <Link
              href="/"
              className="lg:hidden flex items-center justify-center w-10 h-10 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              aria-label="Home"
            >
              <ForumIcon
                size={20}
                className="text-gray-600 hover:text-gray-900 transition-colors duration-200"
              />
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
