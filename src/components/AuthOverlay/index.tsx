'use client';

import Link from 'next/link';

type AuthOverlayPropsI = {
  children: React.ReactNode;
  isAuthenticated: boolean;
  className?: string;
  overlayMessage?: string;
};

const AuthOverlay = ({ 
  children, 
  isAuthenticated, 
  className = '',
  overlayMessage = 'Join <PERSON> to interact with questions and answers'
}: AuthOverlayPropsI) => {
  if (isAuthenticated) {
    return <>{children}</>;
  }

  return (
    <div className={`relative ${className}`}>
      {/* Blurred Content */}
      <div className="filter blur-sm pointer-events-none">
        {children}
      </div>
      
      {/* Overlay */}
      <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4 border border-gray-200">
          <div className="text-center">
            {/* Logo */}
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-[#448600]">
                Navicater
              </h2>
            </div>
            
            {/* Message */}
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Welcome to Navicater
            </h3>
            <p className="text-sm text-gray-600 mb-6">
              {overlayMessage}
            </p>
            
            {/* Action Buttons */}
            <div className="space-y-3">
              <Link
                href="/signup"
                className="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-[#448600] hover:bg-[#357000] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#448600] transition-colors duration-200"
              >
                Sign Up
              </Link>
              
              <Link
                href="/login"
                className="w-full inline-flex justify-center items-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#448600] transition-colors duration-200"
              >
                Sign In
              </Link>
            </div>
            
            {/* Additional Info */}
            <div className="mt-6 pt-4 border-t border-gray-200">
              <p className="text-xs text-gray-500">
                Join thousands of maritime professionals sharing knowledge and expertise
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthOverlay;
