'use client';

import Link from 'next/link';

type AuthOverlayPropsI = {
  children: React.ReactNode;
  isAuthenticated: boolean;
  className?: string;
  maxHeight?: number; // Maximum height before cutoff (like Quora/Medium)
  showGradient?: boolean; // Whether to show gradient fade
};

const AuthOverlay = ({
  children,
  isAuthenticated,
  className = '',
  maxHeight = 10,
  showGradient = true
}: AuthOverlayPropsI) => {
  if (isAuthenticated) {
    return <>{children}</>;
  }

  return (
    <div className={`${className}`}>
      {/* Content container with height limit (like Quora/Medium) */}
      <div
        className="relative overflow-hidden"
        style={{ maxHeight: `${maxHeight}px` }}
      >
        {/* Actual content - fully readable within height limit */}
        <div className="relative">
          {children}
        </div>

        {/* Gradient fade at bottom (like Medium) */}
        {showGradient && (
          <div
            className="absolute bottom-0 left-0 right-0 h-16 pointer-events-none"
            style={{
              background: 'linear-gradient(to bottom, transparent 0%, rgba(255,255,255,0.8) 50%, white 100%)'
            }}
          />
        )}
      </div>

      {/* Continue reading section (like Quora) */}
      <div className="relative bg-white border-t border-gray-100 py-6">
        <div className="text-center">
          {/* <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Continue reading
          </h3> */}
          <p className="text-sm text-gray-600 mb-4">
            Sign up to read the full content and join the discussion
          </p>

          {/* Action Buttons */}
          <div className="flex justify-center space-x-3">
            <Link
              href="/signup"
              className="inline-flex items-center px-6 py-2.5 border border-transparent text-sm font-medium rounded-md text-white bg-[#448600] hover:bg-[#357000] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#448600] transition-colors duration-200"
            >
              Sign up to continue
            </Link>

            <Link
              href="/login"
              className="inline-flex items-center px-6 py-2.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#448600] transition-colors duration-200"
            >
              Sign in
            </Link>
          </div>

          {/* Additional info */}
          {/* <p className="text-xs text-gray-500 mt-4">
            Join thousands of maritime professionals sharing knowledge
          </p> */}
        </div>
      </div>
    </div>
  );
};

export default AuthOverlay;
