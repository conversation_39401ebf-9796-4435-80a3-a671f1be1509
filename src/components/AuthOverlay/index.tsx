'use client';

import Link from 'next/link';

type AuthOverlayPropsI = {
  children: React.ReactNode;
  isAuthenticated: boolean;
  className?: string;
  maxHeight?: string | number; // Maximum height before cutoff (like Quora/Medium)
  showGradient?: boolean; // Whether to show gradient fade
  compact?: boolean; // Compact mode for individual cards
};

const AuthOverlay = ({
  children,
  isAuthenticated,
  className = '',
  maxHeight = '60%',
  showGradient = true,
  compact = false,
}: AuthOverlayPropsI) => {
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // Convert maxHeight to proper CSS value
  const heightValue =
    typeof maxHeight === 'number' ? `${maxHeight}px` : maxHeight;

  return (
    <div className={`${className}`}>
      {/* Content container with height limit (like Quora/Medium) */}
      <div
        className="relative overflow-hidden"
        style={{ maxHeight: heightValue }}
      >
        {/* Actual content - fully readable within height limit */}
        <div className="relative">{children}</div>

        {/* Gradient fade at bottom (like Medium) */}
        {showGradient && (
          <div
            className="absolute bottom-0 left-0 right-0 h-8 pointer-events-none"
            style={{
              background:
                'linear-gradient(to bottom, transparent 0%, rgba(255,255,255,0.7) 30%, white 100%)',
            }}
          />
        )}
      </div>

      {/* Continue reading section - compact for individual cards */}
      <div
        className={`relative bg-white border-t border-gray-100 ${compact ? 'py-3' : 'py-4'} ${compact ? 'shadow-sm' : ''}`}
      >
        <div className="text-center">
          <p
            className={`text-gray-600 mb-3 ${compact ? 'text-xs' : 'text-sm'}`}
          >
            Sign up to read the full content
          </p>

          {/* Action Buttons - compact for individual cards */}
          <div
            className={`flex justify-center ${compact ? 'space-x-2' : 'space-x-3'}`}
          >
            <Link
              href="/signup"
              className={`inline-flex items-center border border-transparent font-medium rounded-md text-white bg-[#448600] hover:bg-[#357000] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#448600] transition-colors duration-200 ${
                compact ? 'px-4 py-1.5 text-xs' : 'px-5 py-2 text-sm'
              }`}
            >
              Sign up
            </Link>

            <Link
              href="/login"
              className={`inline-flex items-center border border-gray-300 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#448600] transition-colors duration-200 ${
                compact ? 'px-4 py-1.5 text-xs' : 'px-5 py-2 text-sm'
              }`}
            >
              Sign in
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthOverlay;
