'use client';

import Link from 'next/link';

type AuthOverlayPropsI = {
  children: React.ReactNode;
  isAuthenticated: boolean;
  className?: string;
  overlayMessage?: string;
  maxReadableHeight?: number; // Height in pixels before content starts fading
  variant?: 'page' | 'card'; // Different styles for different use cases
};

const AuthOverlay = ({
  children,
  isAuthenticated,
  className = '',
  overlayMessage = 'Join <PERSON> to interact with questions and answers',
  maxReadableHeight = 200,
  variant = 'page'
}: AuthOverlayPropsI) => {
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // Calculate fade start position based on maxReadableHeight
  const fadeStartPercentage = Math.min((maxReadableHeight / 400) * 100, 70); // Max 70%

  return (
    <div className={`relative ${className}`}>
      {/* Content with height-based gradient fade */}
      <div className="relative pointer-events-none">
        {/* Normal content */}
        <div className="relative">
          {children}
        </div>

        {/* Gradient fade overlay based on readable height */}
        <div
          className="absolute inset-0 pointer-events-none"
          style={{
            background: `linear-gradient(to bottom, transparent 0%, transparent ${fadeStartPercentage}%, rgba(255,255,255,0.3) ${fadeStartPercentage + 10}%, rgba(255,255,255,0.6) ${fadeStartPercentage + 20}%, rgba(255,255,255,0.9) ${fadeStartPercentage + 30}%, white 100%)`
          }}
        />

        {/* Blur effect for bottom portion */}
        <div
          className="absolute inset-0 pointer-events-none"
          style={{
            background: `linear-gradient(to bottom, transparent 0%, transparent ${fadeStartPercentage + 15}%, rgba(255,255,255,0.1) ${fadeStartPercentage + 25}%, rgba(255,255,255,0.5) 100%)`,
            backdropFilter: `blur(0px)`,
            WebkitBackdropFilter: `blur(0px)`,
            filter: `blur(0px)`
          }}
        />
      </div>

      {/* Compact Sign-in Modal */}
      <div className={`absolute z-10 ${
        variant === 'card'
          ? 'bottom-2 right-2'
          : 'bottom-6 left-1/2 transform -translate-x-1/2'
      }`}>
        <div className={`bg-white rounded-lg shadow-lg border border-gray-200 ${
          variant === 'card'
            ? 'p-3 w-64'
            : 'p-4 w-80'
        }`}>
          <div className="text-center">
            {/* Compact Message */}
            <h3 className={`font-semibold text-gray-900 mb-2 ${
              variant === 'card' ? 'text-xs' : 'text-sm'
            }`}>
              Continue reading
            </h3>
            <p className={`text-gray-600 mb-3 ${
              variant === 'card' ? 'text-xs' : 'text-xs'
            }`}>
              {variant === 'card' ? 'Sign in to read more' : 'Join to view full questions and answers'}
            </p>

            {/* Compact Action Buttons */}
            <div className="flex space-x-2">
              <Link
                href="/signup"
                className={`flex-1 inline-flex justify-center items-center border border-transparent font-medium rounded-md text-white bg-[#448600] hover:bg-[#357000] transition-colors duration-200 ${
                  variant === 'card' ? 'px-3 py-1.5 text-xs' : 'px-4 py-2 text-sm'
                }`}
              >
                Sign Up
              </Link>

              <Link
                href="/login"
                className={`flex-1 inline-flex justify-center items-center border border-gray-300 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200 ${
                  variant === 'card' ? 'px-3 py-1.5 text-xs' : 'px-4 py-2 text-sm'
                }`}
              >
                Sign In
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthOverlay;
