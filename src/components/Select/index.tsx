'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { ChevronDownIcon, CheckIcon } from '@heroicons/react/24/outline';
import { SelectPropsI } from './types';

const Select = React.forwardRef<HTMLButtonElement, SelectPropsI>(
  (
    {
      options,
      value,
      placeholder = 'Select an option',
      onValueChange,
      disabled,
      error,
      className = '',
      label,
      searchable = false,
      onSearch,
      onLoadMore,
      hasMore = false,
      loading = false,
      searchPlaceholder = 'Search...',
      optionValueKey = 'value',
      optionLabelKey = 'label',
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = useState(false);
    const [selectedValue, setSelectedValue] = useState(value || '');
    const [searchTerm, setSearchTerm] = useState('');
    const dropdownRef = useRef<HTMLDivElement>(null);
    const listRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      setSelectedValue(value || '');
    }, [value]);

    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          dropdownRef.current &&
          !dropdownRef.current.contains(event.target as Node)
        ) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      const scrolledPercentage = (scrollTop + clientHeight) / scrollHeight;
      if (scrolledPercentage >= 0.5 && hasMore && !loading && onLoadMore) {
        onLoadMore();
      }
    }, [hasMore, loading, onLoadMore]);

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newSearchTerm = e.target.value;
      setSearchTerm(newSearchTerm);
      if (searchable && onSearch) {
        onSearch(newSearchTerm);
      }
    };

    const handleSelect = (optionValue: string) => {
      setSelectedValue(optionValue);
      setIsOpen(false);
      onValueChange?.(optionValue);
    };

    const selectedOption = options.find(
      (option) => option[optionValueKey] === selectedValue
    );

    const filteredOptions = searchable
      ? options.filter((opt) =>
        String(opt[optionLabelKey]).toLowerCase().includes(searchTerm.toLowerCase())
      )
      : options;

    return (
      <div className={`relative ${className}`}>
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {label}
          </label>
        )}
        <div className="relative" ref={dropdownRef}>
          <button
            ref={ref}
            type="button"
            className={`
              relative w-full cursor-default rounded-md border bg-white py-2 pl-3 pr-10 text-left shadow-sm 
              focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary sm:text-sm
              ${error ? 'border-red-300' : 'border-gray-300'}
              ${disabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : 'hover:border-gray-400'}
            `}
            aria-haspopup="listbox"
            aria-expanded={isOpen}
            onClick={() => !disabled && setIsOpen(!isOpen)}
            disabled={disabled}
          >
            <span className="block truncate">
              {selectedOption ? selectedOption[optionLabelKey] : placeholder}
            </span>
            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <ChevronDownIcon
                className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''
                  }`}
                aria-hidden="true"
              />
            </span>
          </button>

          {isOpen && (
            <div className="absolute z-10 mt-1 w-full rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
              {searchable && (
                <div className="px-3 py-2 border-b border-gray-200">
                  <input
                    type="text"
                    placeholder={searchPlaceholder}
                    className="w-full border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                    value={searchTerm}
                    onChange={handleSearch}
                    autoFocus
                  />
                </div>
              )}
              <div
                ref={listRef}
                className="max-h-60 overflow-auto"
                onScroll={handleScroll}
              >
                {filteredOptions.length === 0 ? (
                  <div className="px-3 py-2 text-sm text-gray-500">
                    No options found
                  </div>
                ) : (
                  filteredOptions.map((option, index) => (
                    <div
                      key={index}
                      className={`
                        relative cursor-default select-none py-2 pl-3 pr-9 
                        ${option.disabled
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-gray-900 cursor-pointer hover:bg-primary hover:text-white'
                        }
                        ${selectedValue === option[optionValueKey] ? 'bg-primary text-white' : ''}
                      `}
                      onClick={() =>
                        !option.disabled && handleSelect(option[optionValueKey])
                      }
                    >
                      <span
                        className={`block truncate ${selectedValue === option[optionValueKey]
                          ? 'font-medium'
                          : 'font-normal'
                          }`}
                      >
                        {option[optionLabelKey]}
                      </span>
                      {selectedValue === option[optionValueKey] && (
                        <span className="absolute inset-y-0 right-0 flex items-center pr-4">
                          <CheckIcon className="h-5 w-5" aria-hidden="true" />
                        </span>
                      )}
                    </div>
                  ))
                )}
                {loading && (
                  <div className="flex justify-center items-center p-2 text-sm text-gray-500">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    <span className="ml-2">Loading more...</span>
                  </div>
                )}

                {!loading && filteredOptions.length === 0 && searchTerm && (
                  <div className="px-3 py-2 text-center text-gray-500 text-sm">
                    No results found
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
      </div>
    );
  }
);

Select.displayName = 'Select';

export { Select };
