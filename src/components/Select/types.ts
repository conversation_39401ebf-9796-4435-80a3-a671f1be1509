export type SelectOptionI = {
  value: string;
  label: string;
  disabled?: boolean;
  [key: string]: any;
};

export type SelectPropsI = {
  options: Record<string, any>[];
  value?: string;
  placeholder?: string;
  onValueChange?: (value: string) => void;
  disabled?: boolean;
  error?: string;
  className?: string;
  label?: string;
  searchable?: boolean;
  onSearch?: (query: string) => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
  loading?: boolean;
  searchPlaceholder?: string;
  optionValueKey?: string; // default = "value"
  optionLabelKey?: string;
};