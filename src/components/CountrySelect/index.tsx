'use client';

import React from 'react';
import { Controller, Control } from 'react-hook-form';
import { Select } from '@/components';
import useCountries from './useHook';

type CountrySelectPropsI = {
    control: Control<any>;
    name: string;
    label?: string;
    placeholder?: string;
    isRequired?: boolean;
    disabled?: boolean;
    className?: string;
};

const CountrySelect = ({
    control,
    name,
    label = 'Country',
    placeholder = 'Select your country',
    isRequired = false,
    disabled = false,
    className = 'w-full'
}: CountrySelectPropsI) => {

    const { countries, loading, loadMore, hasMore, onSearch } = useCountries()

    return (
        <Controller
            control={control}
            name={name}
            rules={{
                required: isRequired ? 'Please select a country' : false,
            }}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
                <Select
                    label={label}
                    placeholder={placeholder}
                    options={countries ?? []}
                    value={value}
                    onValueChange={onChange}
                    onSearch={onSearch}
                    error={error?.message}
                    disabled={disabled}
                    className={className}
                    loading={loading}
                    onLoadMore={loadMore}
                    hasMore={hasMore}
                    searchable={true}
                    optionLabelKey='name'
                    optionValueKey='iso2'
                    searchPlaceholder="Search countries..."
                />
            )}
        />
    );
};

export default CountrySelect;
