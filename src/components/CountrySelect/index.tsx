'use client';

import React from 'react';
import { Control } from 'react-hook-form';
import { ApiSelect } from '@/components';
import { fetchCountries } from '@/networks/data/country';

type CountrySelectPropsI = {
    control: Control<any>;
    name: string;
    label?: string;
    placeholder?: string;
    isRequired?: boolean;
    disabled?: boolean;
    className?: string;
};

const CountrySelect = ({
    control,
    name,
    label = 'Country',
    placeholder = 'Select your country',
    isRequired = false,
    disabled = false,
    className = 'w-full'
}: CountrySelectPropsI) => {

    return (
        <ApiSelect
            control={control}
            name={name}
            label={label}
            placeholder={placeholder}
            isRequired={isRequired}
            disabled={disabled}
            className={className}
            searchPlaceholder="Search countries..."
            apiCall={fetchCountries}
            optionLabelKey="name"
            optionValueKey="iso2"
            uniqueKey="iso2"
            requiredMessage="Please select a country"
        />
    );
};

export default CountrySelect;
