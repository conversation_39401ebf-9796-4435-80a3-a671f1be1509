'use client';

import React from 'react';
import { ApiSelect } from '@/components';
import { OrganizationSelectPropsI } from './types';
import { ApiSelectParamsI, ApiSelectResponseI } from '../ApiSelect/types';
import { apiCall } from '@/lib/api';

const fetchOrganizations = async (
  params: ApiSelectParamsI
): Promise<ApiSelectResponseI> => {
  if (params.search === '' || params.search === undefined) {
    params.search = 'a';
  }
  const response = await apiCall(
    '/backend/api/v1/company/entity/organization/options',
    'GET',
    {
      isAuth: true,
      query: { ...params, page: String(params.page) },
    }
  );
  console.log(response, 'responseOfOrganisations');
  return response as ApiSelectResponseI;
};

const OrganizationSelect = ({
  control,
  name,
  label = 'Organization',
  placeholder = 'Select your organization',
  isRequired = false,
  disabled = false,
  className = 'w-full',
  onChange,
}: OrganizationSelectPropsI) => {
  return (
    <ApiSelect
      control={control}
      name={name}
      label={label}
      placeholder={placeholder}
      isRequired={isRequired}
      disabled={disabled}
      className={className}
      searchPlaceholder="Search organizations..."
      apiCall={fetchOrganizations}
      onChangeOption={onChange}
      optionLabelKey="name"
      optionValueKey="id"
      uniqueKey="id"
      requiredMessage="Please select an organization"
    />
  );
};

export default OrganizationSelect;
