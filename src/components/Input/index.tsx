'use client';

import { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/utilities';

const inputVariants = cva(
  'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-600 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default: '',
        destructive:
          'border-destructive focus-visible:ring-destructive text-destructive',
        ghost: 'border-transparent shadow-none',
        filled: 'bg-muted border-input',
      },
      inputSize: {
        // Changed from 'size' to 'inputSize' to avoid conflict
        sm: 'h-8 px-2 text-xs',
        default: 'h-10 px-3 py-2',
        lg: 'h-12 px-4 text-base',
      },
    },
    defaultVariants: {
      variant: 'default',
      inputSize: 'default', // Updated to match the new name
    },
  }
);

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>, // Omit the native size prop
    VariantProps<typeof inputVariants> {
  label?: string;
  error?: string;
  helperText?: string;
  required?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  leftAddon?: React.ReactNode;
  rightAddon?: React.ReactNode;
  containerClassName?: string;
  labelClassName?: string;
  errorClassName?: string;
  helperTextClassName?: string;
  loading?: boolean;
  success?: boolean;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      variant,
      inputSize, // Changed from 'size' to 'inputSize'
      type = 'text',
      label,
      error,
      helperText,
      required,
      leftIcon,
      rightIcon,
      leftAddon,
      rightAddon,
      containerClassName,
      labelClassName,
      errorClassName,
      helperTextClassName,
      loading,
      success,
      disabled,
      id,
      ...props
    },
    ref
  ) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = !!error;
    const hasSuccess = success && !hasError;
    const isDisabled = disabled || loading;

    return (
      <div className={cn('w-full space-y-1', containerClassName)}>
        {label && (
          <label
            // htmlFor={inputId}
            className={cn(
              'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
              required &&
                "after:content-['*'] after:ml-0.5 after:text-destructive",
              isDisabled && 'opacity-70',
              labelClassName
            )}
          >
            {label}
          </label>
        )}

        <div className="relative flex items-center">
          {/* Left Addon */}
          {leftAddon && (
            <div className="absolute left-0 top-0 h-full flex items-center">
              <span className="px-3 text-muted-foreground text-sm bg-muted border border-r-0 border-input rounded-l-md h-full flex items-center">
                {leftAddon}
              </span>
            </div>
          )}

          {/* Left Icon */}
          {leftIcon && !leftAddon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
              {leftIcon}
            </div>
          )}

          {/* Input */}
          <input
            ref={ref}
            type={type}
            id={inputId}
            disabled={isDisabled}
            className={cn(
              inputVariants({
                variant: hasError ? 'destructive' : variant,
                inputSize,
              }), // Updated to inputSize
              leftIcon && !leftAddon ? 'pl-10' : '',
              leftAddon ? 'pl-[calc(theme(spacing.10)+2px)]' : '',
              rightIcon || rightAddon || loading ? 'pr-10' : '',
              hasSuccess && 'border-success focus-visible:ring-success',
              className
            )}
            aria-invalid={hasError}
            aria-describedby={
              hasError
                ? `${inputId}-error`
                : helperText
                  ? `${inputId}-helper`
                  : undefined
            }
            {...props}
          />

          {/* Right side content */}
          {(rightIcon || rightAddon || loading || hasSuccess || hasError) && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
              {/* Loading spinner */}
              {loading && (
                <svg
                  className="h-4 w-4 animate-spin"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              )}

              {/* Success icon */}
              {hasSuccess && !loading && (
                <svg
                  className="h-4 w-4 text-success"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              )}

              {/* Error icon */}
              {hasError && !loading && (
                <svg
                  className="h-4 w-4 text-destructive"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              )}

              {/* Right Icon */}
              {rightIcon && !hasError && !hasSuccess && !loading && (
                <div className="text-muted-foreground">{rightIcon}</div>
              )}
            </div>
          )}

          {/* Right Addon */}
          {rightAddon && (
            <div className="absolute right-0 top-0 h-full flex items-center">
              <span className="px-3 text-muted-foreground text-sm bg-muted border border-l-0 border-input rounded-r-md h-full flex items-center">
                {rightAddon}
              </span>
            </div>
          )}
        </div>

        {/* Helper Text */}
        {helperText && !hasError && (
          <p
            id={`${inputId}-helper`}
            className={cn('text-sm text-muted-foreground', helperTextClassName)}
          >
            {helperText}
          </p>
        )}

        {/* Error Message */}
        {hasError && (
          <p
            id={`${inputId}-error`}
            className={cn('text-sm text-red-600', errorClassName)}
          >
            {error}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';
