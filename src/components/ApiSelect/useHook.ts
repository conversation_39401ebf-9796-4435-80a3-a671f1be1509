import { useEffect, useState } from "react";
import { ApiSelectOptionI, ApiSelectResponseI, ApiSelectParamsI } from "./types";

type UseApiSelectPropsI = {
    apiCall: (params: ApiSelectParamsI) => Promise<ApiSelectResponseI>;
    uniqueKey: string;
};

const useApiSelect = ({ apiCall, uniqueKey }: UseApiSelectPropsI) => {
    const [options, setOptions] = useState<ApiSelectOptionI[]>([]);
    const [hasMore, setHasMore] = useState(false);
    const [loading, setLoading] = useState(false);
    const [pageNum, setPageNum] = useState(1);
    const [searchTerm, setSearchTerm] = useState<string>('');

    const fetchOptionsAsync = async ({ page = 1, search }: { page: number, search?: string }): Promise<ApiSelectResponseI | void> => {
        try {
            setLoading(true);
            const params = { page: String(page), search: search?.trim() || undefined };
            const result = await apiCall(params);
            
            setOptions(prevOptions => {
                const optionMap = new Map<string, ApiSelectOptionI>();
                
                // Add existing options to map
                if (page !== 1) {
                    prevOptions.forEach(option => {
                        optionMap.set(option[uniqueKey], option);
                    });
                }
                
                // Add new options to map (will overwrite duplicates)
                result.data.forEach(option => {
                    optionMap.set(option[uniqueKey], option);
                });
                
                const newOptions = Array.from(optionMap.values());
                const totalLoadedAfterThis = newOptions.length;
                setHasMore(result.total > totalLoadedAfterThis);
                return newOptions;
            });
            
            return result;
        } catch (error) {
            console.error('Error fetching options:', error);
        } finally {
            setLoading(false);
        }
    };

    const loadMore = () => {
        if (hasMore && !loading) {
            const nextPage = pageNum + 1;
            setPageNum(nextPage);
            fetchOptionsAsync({
                page: nextPage,
                search: searchTerm
            });
        }
    };

    const reset = () => {
        setOptions([]);
        setPageNum(1);
        setHasMore(false);
        fetchOptionsAsync({ page: 1 });
    };

    const onSearch = (text: string) => {
        setSearchTerm(text);
        setOptions([]);
        setPageNum(1);
        setHasMore(false);

        fetchOptionsAsync({ page: 1, search: text.trim() || undefined });
    };

    useEffect(() => {
        fetchOptionsAsync({ page: 1 });
    }, []);

    return {
        options,
        loadMore,
        loading,
        hasMore,
        onSearch,
        reset,
        pageNum,
    };
};

export default useApiSelect;
