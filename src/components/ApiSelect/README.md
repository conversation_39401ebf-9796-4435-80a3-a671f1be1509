# ApiSelect Component

A reusable select component with built-in API integration, search functionality, and pagination support.

## Features

- ✅ **API Integration**: Automatically handles API calls with pagination
- ✅ **Search**: Built-in search functionality with debouncing
- ✅ **Pagination**: Infinite scroll with load more functionality
- ✅ **Deduplication**: Prevents duplicate options using unique keys
- ✅ **Form Integration**: Works seamlessly with react-hook-form
- ✅ **TypeScript**: Full TypeScript support
- ✅ **Customizable**: Flexible configuration for different data sources

## Usage

### Basic Usage

```tsx
import { ApiSelect } from '@/components';
import { fetchCountries } from '@/networks/data/country';

<ApiSelect
  control={control}
  name="countryIso2"
  label="Country"
  placeholder="Select your country"
  isRequired={true}
  apiCall={fetchCountries}
  optionLabelKey="name"
  optionValueKey="iso2"
  uniqueKey="iso2"
/>;
```

### Creating Specific Select Components

```tsx
// CountrySelect.tsx
import { ApiSelect } from '@/components';
import { fetchCountries } from '@/networks/data/country';

const CountrySelect = ({ control, name, ...props }) => (
  <ApiSelect
    control={control}
    name={name}
    apiCall={fetchCountries}
    optionLabelKey="name"
    optionValueKey="iso2"
    uniqueKey="iso2"
    {...props}
  />
);
```

## Props

| Prop                | Type           | Required | Default            | Description                                            |
| ------------------- | -------------- | -------- | ------------------ | ------------------------------------------------------ |
| `control`           | `Control<any>` | ✅       | -                  | React Hook Form control object                         |
| `name`              | `string`       | ✅       | -                  | Form field name                                        |
| `apiCall`           | `Function`     | ✅       | -                  | API function that returns `{data: T[], total: number}` |
| `optionLabelKey`    | `string`       | ✅       | -                  | Key to use for option display text                     |
| `optionValueKey`    | `string`       | ✅       | -                  | Key to use for option value                            |
| `label`             | `string`       | ❌       | -                  | Field label                                            |
| `placeholder`       | `string`       | ❌       | "Select an option" | Placeholder text                                       |
| `isRequired`        | `boolean`      | ❌       | `false`            | Whether field is required                              |
| `disabled`          | `boolean`      | ❌       | `false`            | Whether field is disabled                              |
| `className`         | `string`       | ❌       | "w-full"           | CSS classes                                            |
| `searchPlaceholder` | `string`       | ❌       | "Search..."        | Search input placeholder                               |
| `uniqueKey`         | `string`       | ❌       | `optionValueKey`   | Key for deduplication                                  |
| `requiredMessage`   | `string`       | ❌       | Auto-generated     | Custom required validation message                     |

## API Function Requirements

Your API function must:

1. Accept parameters: `{page: string, search?: string}`
2. Return: `{data: T[], total: number}`

```tsx
const fetchYourData = async (params: { page: string; search?: string }) => {
  const response = await apiCall('/your-endpoint', 'GET', {
    query: params,
  });
  return {
    data: response.data,
    total: response.total,
  };
};
```

## Examples

### Country Select

```tsx
<ApiSelect
  control={control}
  name="country"
  label="Country"
  apiCall={fetchCountries}
  optionLabelKey="name"
  optionValueKey="iso2"
  uniqueKey="iso2"
/>
```

### Organization Select

```tsx
<ApiSelect
  control={control}
  name="organization"
  label="Organization"
  apiCall={fetchOrganizations}
  optionLabelKey="name"
  optionValueKey="id"
  uniqueKey="id"
/>
```

### Category Select

```tsx
<ApiSelect
  control={control}
  name="category"
  label="Category"
  apiCall={fetchCategories}
  optionLabelKey="title"
  optionValueKey="slug"
  uniqueKey="id"
/>
```
