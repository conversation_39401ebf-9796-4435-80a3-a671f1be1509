'use client';

import React from 'react';
import { Controller } from 'react-hook-form';
import { Select } from '@/components';
import { ApiSelectPropsI } from './types';
import useApiSelect from './useHook';

const ApiSelect = ({
    control,
    name,
    label,
    placeholder = 'Select an option',
    isRequired = false,
    disabled = false,
    className = 'w-full',
    searchPlaceholder = 'Search...',
    apiCall,
    optionLabelKey,
    optionValueKey,
    uniqueKey,
    requiredMessage
}: ApiSelectPropsI) => {

    const { options, loading, loadMore, hasMore, onSearch } = useApiSelect({
        apiCall,
        uniqueKey: uniqueKey || optionValueKey
    });

    return (
        <Controller
            control={control}
            name={name}
            rules={{
                required: isRequired ? (requiredMessage || `Please select ${label?.toLowerCase() || 'an option'}`) : false,
            }}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
                <Select
                    label={label}
                    placeholder={placeholder}
                    options={options ?? []}
                    value={value}
                    onValueChange={onChange}
                    onSearch={onSearch}
                    error={error?.message}
                    disabled={disabled}
                    className={className}
                    loading={loading}
                    onLoadMore={loadMore}
                    hasMore={hasMore}
                    searchable={true}
                    optionLabelKey={optionLabelKey}
                    optionValueKey={optionValueKey}
                    searchPlaceholder={searchPlaceholder}
                />
            )}
        />
    );
};

export default ApiSelect;
