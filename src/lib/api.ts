import { isFilled, omit } from '@/utilities/data/object';
import { generateUrl } from '@/utilities/networks/api';
import APIResError from '@/errors/networks/APIResError';
import AppError from '@/errors/networks/AppError';
import { getOrGenerateDeviceId } from '@/utils/deviceId';
import { parseApiError } from '@/utils/errorParser';
import { showToast } from '@/utils/toast';
import { getSession } from 'next-auth/react';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

export type MethodI = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
export type HeadersI = Record<string, string>;

export type APICallI<PayloadT = unknown> = {
  isAuth?: boolean;
  payload?: PayloadT;
  query?: any;
  routeId?: string | number;
  headers?: HeadersI;
};

const getHeaders = async (): Promise<HeadersI> => {
  const deviceId = getOrGenerateDeviceId();

  return {
    'x-api-key': process.env.NEXT_PUBLIC_API_KEY || '',
    Accept: 'application/json',
    'x-device-id': deviceId,
    'x-platform': 'web_app',
    'x-version-no': process.env.NEXT_PUBLIC_VERSION_NO || '3.0',
  };
};

export const apiCall = async <PayloadT = unknown, ResponseT = unknown>(
  path: string,
  method: MethodI,
  { isAuth = true, payload, query, routeId, headers }: APICallI<PayloadT>
): Promise<ResponseT> => {
  try {
    const baseUrl = BASE_URL;

    const url = generateUrl({
      baseUrl,
      path,
      query,
      routeId,
    });

    let baseHeaders = await getHeaders();

    if (isFilled(headers)) {
      baseHeaders = { ...baseHeaders, ...headers };
    }

    if (['POST', 'PATCH', 'PUT'].includes(method) && isFilled(payload)) {
      baseHeaders['Content-Type'] = 'application/json';
    }

    if (method === 'DELETE') {
      baseHeaders = omit(baseHeaders, ['Content-Type', 'Accept']) as HeadersI;
    }

    if (isAuth) {
      const session = await getSession();
      const token = typeof window !== 'undefined' ? session?.token : null;

      if (token) {
        baseHeaders.Authorization = `Bearer ${token}`;
      }
    }

    const options: RequestInit = {
      headers: baseHeaders,
      method,
      credentials: 'include',
    };

    if (isFilled(payload)) {
      options.body = JSON.stringify(payload);
    }

    const response = await fetch(url, options);

    const contentType = response.headers.get('content-type');
    const isJson = contentType?.includes('application/json');
    const json = isJson ? await response.json() : null;
    console.log(json, payload, query, 'jsonChecking.......')
    if (response.status >= 200 && response.status < 300) {
      return json as ResponseT;
    } else if (response.status === 401) {
      // Clear auth state on unauthorized
      if (typeof window !== 'undefined') {
        localStorage.removeItem('token');
        localStorage.removeItem('jwtToken');
      }

      // Parse and show auth error
      const parsedError = parseApiError(json);
      showToast({
        type: 'error',
        message: parsedError.message,
        description: parsedError.description,
      });
    } else {
      // Parse and show API error
      const parsedError = parseApiError(json);
      showToast({
        type: 'error',
        message: parsedError.message,
        description: parsedError.description,
      });
    }

    throw new APIResError(
      response.status,
      json || { message: 'No response body' }
    );
  } catch (error) {
    if (error instanceof APIResError) {
      throw error;
    } else if (error instanceof TypeError) {
      // Network error
      showToast({
        type: 'error',
        message: 'Network Error',
        description: 'Please check your internet connection and try again.',
      });
      throw error;
    }

    // Unknown error
    const parsedError = parseApiError(error);
    showToast({
      type: 'error',
      message: parsedError.message,
      description: parsedError.description,
    });
    throw new AppError('Unknown error', error as Error);
  }
};
