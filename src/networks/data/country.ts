import { apiCall } from '@/lib/api';
import { FetchCountryParamsI, FetchCountryResponseI } from './types';

export const fetchCountries = async (
  params: FetchCountryParamsI
): Promise<FetchCountryResponseI> => {
  return await apiCall<FetchCountryParamsI, FetchCountryResponseI>(
    '/backend/api/v1/master/country/options',
    'GET',
    {
      isAuth: true,
      query: { ...params, pageSize: 10 },
    }
  );
};
