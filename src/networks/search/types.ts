export interface OptionSearchBodyI {
  search?: string;
  page?: string;
  type?: string;
}

export interface OptionSearchResultI {
  id: string;
  name: string;
  dataType: string;
}

export interface CountryOptionSearchResultI {
  iso2: string;
  name: string;
}

export interface EntitySearchResponse {
  data: OptionSearchResultI[];
  total: number;
}

export interface SearchResultI {
  id: string;
  name: string;
  dataType: string;
}
