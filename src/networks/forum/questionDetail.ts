import { apiCall } from '@/lib/api';
import { AnswerDetailI, QuestionDetailI } from './types';
export type AnswerI = AnswerDetailI;

/**
 * Fetch question details with answers by ID
 */
export const fetchQuestionByIdAPI = async (
  id: string
): Promise<QuestionDetailI> => {
  const result = await apiCall<null, QuestionDetailI>(
    `/backend/api/v1/forum/question/${id}`,
    'GET',
    { isAuth: true }
  );
  return result;
};

export const fetchQuestionIdBySlug = async (
  slug: string
): Promise<{ id: string }> => {
  const result = await apiCall<null, { id: string }>(
    `/backend/api/v1/forum/question/slug/${slug}`,
    'GET',
    { isAuth: true }
  );
  return result;
};
