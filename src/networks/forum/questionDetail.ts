import { apiCall } from '../apiCall';
import { ForumQuestionListingI } from './types';

export type QuestionDetailI = ForumQuestionListingI & {
  profile: {
    id: string;
    name: string;
    avatar: string | null;
    designation: string;
    entity: string;
  };
};

export type AnswerI = {
  id: string;
  content: string;
  createdAt: string;
  upvoteCount: number;
  downvoteCount: number;
  isVerified: boolean;
  profile: {
    id: string;
    name: string;
    avatar: string | null;
    designation: string;
    entity: string;
  };
  media: Array<{
    id: string;
    fileUrl: string;
    fileExtension: string;
  }>;
};

export type QuestionDetailResponseI = {
  question: QuestionDetailI;
  answers: AnswerI[];
};

/**
 * Fetch question details by slug
 */
export const fetchQuestionBySlugAPI = async (slug: string): Promise<QuestionDetailI> => {
  const result = await apiCall<null, QuestionDetailI>(
    `/backend/api/v1/forum/question/slug/${slug}`,
    'GET',
    { isAuth: true }
  );
  return result;
};

/**
 * Fetch answers for a question by slug
 */
export const fetchAnswersBySlugAPI = async (slug: string): Promise<AnswerI[]> => {
  const result = await apiCall<null, AnswerI[]>(
    `/backend/api/v1/forum/answer/slug/${slug}`,
    'GET',
    { isAuth: true }
  );
  return result;
};
