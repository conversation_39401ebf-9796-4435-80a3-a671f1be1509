import { v4 as uuidv4 } from 'uuid';

const DEVICE_ID_KEY = 'deviceId';

// Temporary device ID for server-side rendering
let tempDeviceId: string | null = null;

/**
 * Gets or generates a device ID that persists across sessions
 * This ensures the same device ID is used for all API calls
 */
export const getOrGenerateDeviceId = (): string => {
  // For server-side rendering, use a temporary device ID
  if (typeof window === 'undefined') {
    if (!tempDeviceId) {
      tempDeviceId = uuidv4();
    }
    return tempDeviceId;
  }

  try {
    let deviceId = localStorage.getItem(DEVICE_ID_KEY);

    if (!deviceId) {
      deviceId = uuidv4();
      localStorage.setItem(DEVICE_ID_KEY, deviceId);
    }

    return deviceId;
  } catch (error) {
    console.error('Error accessing localStorage for device ID:', error);
    // Fallback to generating a temporary UUID if localStorage fails
    return uuidv4();
  }
};

/**
 * Sets a specific device ID (useful for testing or when receiving from server)
 */
export const setDeviceId = (deviceId: string): void => {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.setItem(DEVICE_ID_KEY, deviceId);
  } catch (error) {
    console.error('Error setting device ID in localStorage:', error);
  }
};

/**
 * Clears the stored device ID (useful for logout or reset)
 */
export const clearDeviceId = (): void => {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.removeItem(DEVICE_ID_KEY);
  } catch (error) {
    console.error('Error clearing device ID from localStorage:', error);
  }
};

/**
 * Gets the current device ID without generating a new one
 */
export const getCurrentDeviceId = (): string | null => {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    return localStorage.getItem(DEVICE_ID_KEY);
  } catch (error) {
    console.error('Error getting device ID from localStorage:', error);
    return null;
  }
};
