import toast from 'react-hot-toast';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export type ShowToastParams = {
  type: ToastType;
  message: string;
  description?: string;
  duration?: number;
};

/**
 * Show a toast notification using default styling
 */
export const showToast = ({
  type,
  message,
  description,
  duration = 4000,
}: ShowToastParams) => {
  const content = description ? `${message}\n${description}` : message;

  switch (type) {
    case 'success':
      return toast.success(content, { duration });
    case 'error':
      return toast.error(content, { duration });
    case 'warning':
      return toast(content, { duration, icon: '⚠️' });
    case 'info':
      return toast(content, { duration, icon: 'ℹ️' });
    default:
      return toast(content, { duration });
  }
};

export default showToast;
