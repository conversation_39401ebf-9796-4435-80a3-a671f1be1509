import { getSession } from 'next-auth/react';

export type SessionData = {
  profileId: string;
  username?: string;
  email: string;
  name?: string;
  designationText?: string;
  entityText?: string;
  avatar?: string;
  isEmailVerified: boolean;
  isUsernameSaved: boolean;
  isPersonalDetailsSaved: boolean;
  isWorkDetailsSaved: boolean;
  isPrivacyPolicyAccepted: boolean;
  previousStatus: string;
  designation?: string;
  entity?: string;
  token: string;
  jwtToken: string;
  deviceId: string;
};

/**
 * Get session data with proper typing
 */
export const getSessionData = async (): Promise<SessionData | null> => {
  try {
    const session = await getSession();
    if (!session) return null;

    return {
      profileId: (session as any).profileId,
      username: (session as any).username,
      email: session.user?.email || '',
      name: session.user?.name,
      designationText: (session as any).designationText,
      entityText: (session as any).entityText,
      avatar: (session as any).avatar,
      isEmailVerified: (session as any).isEmailVerified,
      isUsernameSaved: (session as any).isUsernameSaved,
      isPersonalDetailsSaved: (session as any).isPersonalDetailsSaved,
      isWorkDetailsSaved: (session as any).isWorkDetailsSaved,
      isPrivacyPolicyAccepted: (session as any).isPrivacyPolicyAccepted,
      previousStatus: (session as any).previousStatus,
      designation: (session as any).designation,
      entity: (session as any).entity,
      token: (session as any).token,
      jwtToken: (session as any).jwtToken,
      deviceId: (session as any).deviceId,
    };
  } catch (error) {
    console.error('Error getting session data:', error);
    return null;
  }
};

/**
 * Get specific session values
 */
export const getSessionValue = async (key: keyof SessionData): Promise<any> => {
  const sessionData = await getSessionData();
  return sessionData?.[key] || null;
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = async (): Promise<boolean> => {
  const sessionData = await getSessionData();
  return !!sessionData?.profileId;
};

/**
 * Check if user has completed specific steps
 */
export const hasCompletedStep = async (
  step: 'username' | 'personal' | 'work' | 'email'
): Promise<boolean> => {
  const sessionData = await getSessionData();
  if (!sessionData) return false;

  switch (step) {
    case 'username':
      return sessionData.isUsernameSaved;
    case 'personal':
      return sessionData.isPersonalDetailsSaved;
    case 'work':
      return sessionData.isWorkDetailsSaved;
    case 'email':
      return sessionData.isEmailVerified;
    default:
      return false;
  }
};

export default getSessionData;
