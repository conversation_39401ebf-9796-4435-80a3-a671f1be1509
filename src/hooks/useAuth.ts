'use client';

import { useSession } from 'next-auth/react';
import { UserI } from '@/networks/forum/types';

type UseAuthReturnI = {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserI | null;
  session: any;
};

const useAuth = (): UseAuthReturnI => {
  const { data: session, status } = useSession();

  const isLoading = status === 'loading';
  const isAuthenticated = status === 'authenticated' && !!session?.user;

  const user: UserI | null = session?.user
    ? {
        id: (session as any).profileId || session.user.id || '',
        name: session.user.name || '',
        email: session.user.email || '',
        avatar: session.user.image || (session as any).avatar || '',
        title: (session as any).designationText || '',
        company: (session as any).entityText || '',
        location: '',
        // connections: 0,
        // profileViews: 0,
        // postImpressions: 0,
        isVerified: (session as any).isEmailVerified || false,
      }
    : null;

  return {
    isAuthenticated,
    isLoading,
    user,
    session,
  };
};

export default useAuth;
