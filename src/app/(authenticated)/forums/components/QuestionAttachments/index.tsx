import { MediaFileI } from '@/networks/forum/types';
import React from 'react';

const QuestionAttachments = ({
  nonImageFiles,
}: {
  nonImageFiles: MediaFileI[];
}) => {
  const getFileIcon = (type: MediaFileI['type']) => {
    switch (type) {
      case 'pdf':
        return '📄';
      case 'doc':
        return '📝';
      case 'sheet':
        return '📊';
      case 'video':
        return '🎥';
      case 'audio':
        return '🎵';
      default:
        return '📎';
    }
  };

  const handleFileClick = (url: string) => {
    window.open(url, '_blank');
  };
  return (
    nonImageFiles.length > 0 && (
      <div className="mb-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Attachments:</h4>
        <div className="space-y-2">
          {nonImageFiles.map(file => (
            <button
              key={file.id}
              onClick={() => handleFileClick(file.url)}
              className="flex items-center space-x-2 p-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors w-full text-left"
            >
              <span className="text-lg">{getFileIcon(file.type)}</span>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {file.name}
                </p>
                <p className="text-xs text-gray-500">
                  {file.type.toUpperCase()}
                </p>
              </div>
              <span className="text-xs text-gray-400">↗</span>
            </button>
          ))}
        </div>
      </div>
    )
  );
};

export default QuestionAttachments;
