import React from 'react';
import Image from 'next/image';
import { useState, useEffect } from 'react';

const QuestionImageViewer = ({ allImages }: { allImages: string[] }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalImageIndex, setModalImageIndex] = useState(0);

  const nextImage = () => {
    if (allImages && allImages.length > 1) {
      setCurrentImageIndex(prev =>
        prev === allImages.length - 1 ? 0 : prev + 1
      );
    }
  };

  const prevImage = () => {
    if (allImages && allImages.length > 1) {
      setCurrentImageIndex(prev =>
        prev === 0 ? allImages.length - 1 : prev - 1
      );
    }
  };

  const nextModalImage = () => {
    if (allImages && allImages.length > 1) {
      setModalImageIndex(prev =>
        prev === allImages.length - 1 ? 0 : prev + 1
      );
    }
  };

  const prevModalImage = () => {
    if (allImages && allImages.length > 1) {
      setModalImageIndex(prev =>
        prev === 0 ? allImages.length - 1 : prev - 1
      );
    }
  };

  const openModal = (imageIndex: number) => {
    setModalImageIndex(imageIndex);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isModalOpen) return;

      if (e.key === 'Escape') {
        closeModal();
      } else if (e.key === 'ArrowLeft') {
        prevModalImage();
      } else if (e.key === 'ArrowRight') {
        nextModalImage();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isModalOpen, allImages.length]);

  return (
    <>
      {allImages.length > 0 && (
        <div className="mb-4">
          <div className="relative">
            {allImages.length === 1 ? (
              <div
                className="relative rounded-lg overflow-hidden cursor-pointer"
                onClick={() => openModal(0)}
              >
                <Image
                  src={allImages[0]}
                  alt="Question image"
                  width={600}
                  height={300}
                  className="w-full h-auto object-cover hover:opacity-90 transition-opacity"
                />
                <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black bg-opacity-20">
                  <span className="text-white text-sm font-medium bg-black bg-opacity-50 px-3 py-1 rounded-full">
                    Click to view
                  </span>
                </div>
              </div>
            ) : (
              <div className="relative rounded-lg overflow-hidden">
                <div
                  className="cursor-pointer"
                  onClick={() => openModal(currentImageIndex)}
                >
                  <Image
                    src={allImages[currentImageIndex]}
                    alt={`Question image ${currentImageIndex + 1}`}
                    width={600}
                    height={300}
                    className="w-full h-auto object-cover hover:opacity-90 transition-opacity"
                  />
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-transparent bg-opacity-20">
                    <span className="text-white text-sm font-medium bg-gray-800 opacity-80 bg-opacity-50 px-3 py-1 rounded-full">
                      Click to view
                    </span>
                  </div>
                </div>

                <button
                  onClick={prevImage}
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all z-10"
                >
                  ←
                </button>

                <button
                  onClick={nextImage}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all z-10"
                >
                  →
                </button>

                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1 z-10">
                  {allImages.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`w-2 h-2 rounded-full transition-all ${
                        index === currentImageIndex
                          ? 'bg-white'
                          : 'bg-white bg-opacity-50'
                      }`}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Image Modal */}
      {isModalOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90"
          onClick={closeModal}
        >
          <div
            className="relative max-w-7xl max-h-full p-4"
            onClick={e => e.stopPropagation()}
          >
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 z-10 text-white hover:text-gray-300 transition-colors"
            >
              <svg
                className="w-8 h-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>

            <div className="relative">
              <Image
                src={allImages[modalImageIndex]}
                alt={`Question image ${modalImageIndex + 1}`}
                width={1200}
                height={800}
                className="max-w-full max-h-[90vh] object-contain"
              />

              {allImages.length > 1 && (
                <>
                  <button
                    onClick={prevModalImage}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-12 h-12 flex items-center justify-center hover:bg-opacity-70 transition-all"
                  >
                    ←
                  </button>

                  <button
                    onClick={nextModalImage}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-12 h-12 flex items-center justify-center hover:bg-opacity-70 transition-all"
                  >
                    →
                  </button>

                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    {allImages.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setModalImageIndex(index)}
                        className={`w-3 h-3 rounded-full transition-all ${
                          index === modalImageIndex
                            ? 'bg-white'
                            : 'bg-white bg-opacity-50'
                        }`}
                      />
                    ))}
                  </div>
                </>
              )}
            </div>

            <div className="absolute bottom-4 left-4 text-white text-sm bg-black bg-opacity-50 px-3 py-1 rounded-full">
              {modalImageIndex + 1} of {allImages.length}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default QuestionImageViewer;
