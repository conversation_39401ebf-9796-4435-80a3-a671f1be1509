'use client';

import React, { useState, useEffect } from 'react';
import ProfileSidebar from '../ProfileSidebar';
import QuestionFeed from '../QuestionFeed';
import MobileAppPromotion from '../MobileAppPromotion';
import { ProfileSidebarSkeleton } from '../Shimmer';
import { DashboardLayoutPropsI } from './types';
import { Header } from '@/components';

const DashboardLayout = ({
  user,
  initialQuestions = [],
  isLoading = false,
  onLoadMoreQuestions,
  hasMoreQuestions = true,
}: DashboardLayoutPropsI) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return null;
  }

  return (
    <>
      <Header />
      <main className="py-20">
        <div className="max-w-screen-xl mx-auto px-4">
          {/* Mobile ProfileSidebar */}
          <div className="lg:hidden mb-6">
            {user ? (
              <ProfileSidebar user={user} isMobile={true} />
            ) : (
              <ProfileSidebarSkeleton isMobile={true} />
            )}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
            {/* Desktop ProfileSidebar */}
            <div className="hidden lg:block lg:col-span-3">
              <div className="sticky top-20">
                {user ? (
                  <ProfileSidebar user={user} isMobile={false} />
                ) : (
                  <ProfileSidebarSkeleton />
                )}
              </div>
            </div>

            <section className="lg:col-span-6 col-span-1">
              <div className="space-y-6">
                <QuestionFeed
                  initialQuestions={initialQuestions}
                  onLoadMore={onLoadMoreQuestions}
                  hasMore={hasMoreQuestions}
                  isLoading={isLoading}
                />
              </div>
            </section>

            <aside className="hidden lg:block lg:col-span-3">
              <div className="sticky top-20 space-y-6">
                <MobileAppPromotion />
              </div>
            </aside>
          </div>
        </div>
      </main>
    </>
  );
};

export default DashboardLayout;
