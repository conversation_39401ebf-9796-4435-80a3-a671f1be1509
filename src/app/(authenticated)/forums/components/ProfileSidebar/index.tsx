'use client';
import React from 'react';
import Image from 'next/image';
import { ProfileSidebarPropsI } from './types';
import { logout } from '@/app/actions';

const ProfileSidebar = ({ user }: ProfileSidebarPropsI) => {
  const handleLogout = async () => {
    localStorage.clear();
    await logout();
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
      {/* Cover - hidden on mobile */}
      <div className="hidden lg:block h-16 bg-gradient-to-r from-gray-900 to-[#448600] relative">
        {user.coverImage && (
          <Image
            src={user.coverImage}
            alt="Cover"
            fill
            className="object-cover"
          />
        )}
      </div>

      {/* Mobile Layout */}
      <div className="lg:hidden p-4">
        <div className="flex items-center space-x-4">
          {/* Avatar */}
          <div className="flex-shrink-0">
            <Image
              src={user.avatar}
              alt={user.name}
              width={56}
              height={56}
              className="w-14 h-14 rounded-full border-2 border-gray-200 object-cover"
            />
          </div>

          {/* User Info */}
          <div className="flex-1 min-w-0">
            <h2 className="text-lg font-semibold text-gray-900 truncate">
              {user.name}
            </h2>
            <p className="text-sm text-gray-600">
              {user.title} at {user.company}
            </p>
          </div>

          {/* Logout Button */}
          <div className="flex-shrink-0">
            <button
              onClick={handleLogout}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Logout"
            >
              <svg
                className="w-5 h-5"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:block px-4 pb-4 -mt-8 relative">
        <div className="flex justify-center mb-4">
          <div className="relative">
            <Image
              src={user.avatar}
              alt={user.name}
              width={72}
              height={72}
              className="w-18 h-18 rounded-full border-4 border-white object-cover"
            />
          </div>
        </div>

        <div className="text-center mb-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-1">
            {user.name}
          </h2>
        </div>

        <div className="border-t border-gray-200 my-4"></div>

        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-2">Expand your network</p>
          <div className="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded">
            <div className="w-4 h-4 bg-primary rounded-sm mr-2 flex-shrink-0"></div>
            <span className="text-sm font-semibold text-gray-800">
              Join Navicater App
            </span>
          </div>
        </div>

        <div className="border-t border-gray-200 my-4"></div>

        <div className="space-y-2">
          <div
            onClick={handleLogout}
            className="flex items-center text-sm text-gray-600 hover:text-gray-900 cursor-pointer"
          >
            <svg
              className="w-4 h-4 mr-2"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z"
                clipRule="evenodd"
              />
            </svg>
            Logout
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileSidebar;
