'use client';

import React from 'react';

type ShimmerPropsI = {
  className?: string;
};

const Shimmer = ({ className = '' }: ShimmerPropsI) => (
  <div className={`animate-pulse bg-gray-200 rounded ${className}`} />
);

export const QuestionCardSkeleton = () => (
  <div className="bg-white rounded-lg border border-gray-200 p-6 mb-4">
    <div className="flex justify-between items-start mb-4">
      <div className="flex-1">
        <Shimmer className="h-6 w-3/4 mb-2" />
        <Shimmer className="h-6 w-1/2" />
      </div>
      <Shimmer className="w-5 h-5 rounded-full" />
    </div>

    {/* Content */}
    <div className="mb-4">
      <Shimmer className="h-4 w-full mb-2" />
      <Shimmer className="h-4 w-5/6 mb-2" />
      <Shimmer className="h-4 w-4/5" />
    </div>

    {/* Image placeholder */}
    <Shimmer className="h-48 w-full mb-4 rounded-lg" />

    {/* Stats */}
    <div className="flex items-center mb-4">
      <Shimmer className="h-4 w-24 mr-4" />
      <Shimmer className="h-4 w-20" />
    </div>

    {/* Action buttons */}
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <Shimmer className="h-8 w-20 rounded-full" />
        <Shimmer className="h-8 w-24 rounded-full" />
        <Shimmer className="h-8 w-16 rounded-full" />
      </div>
      <Shimmer className="w-8 h-8 rounded-full" />
    </div>

    {/* Tags */}
    <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-gray-100">
      <Shimmer className="h-6 w-16 rounded-full" />
      <Shimmer className="h-6 w-20 rounded-full" />
      <Shimmer className="h-6 w-14 rounded-full" />
    </div>
  </div>
);

export const ProfileSidebarSkeleton = ({ isMobile = false }: { isMobile?: boolean }) => {
  if (isMobile) {
    // Mobile horizontal layout skeleton
    return (
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
        <div className="p-4">
          <div className="flex items-center space-x-4">
            {/* Avatar */}
            <div className="flex-shrink-0">
              <Shimmer className="w-14 h-14 rounded-full border-2 border-gray-200" />
            </div>

            {/* User Info */}
            <div className="flex-1 min-w-0">
              <Shimmer className="h-5 w-32 mb-2" />
              <Shimmer className="h-4 w-24" />
            </div>

            {/* Logout Button */}
            <div className="flex-shrink-0">
              <Shimmer className="w-8 h-8 rounded" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Desktop vertical layout skeleton
  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
      {/* Cover */}
      <Shimmer className="h-16 w-full" />

      {/* Profile Content */}
      <div className="px-4 pb-4 -mt-8 relative">
        {/* Avatar */}
        <div className="flex justify-center mb-4">
          <Shimmer className="w-18 h-18 rounded-full border-4 border-white" />
        </div>

        {/* User Info */}
        <div className="text-center mb-4">
          <Shimmer className="h-5 w-32 mx-auto mb-2" />
          <Shimmer className="h-4 w-24 mx-auto mb-1" />
          <Shimmer className="h-3 w-28 mx-auto" />
        </div>

        {/* Bio */}
        <div className="mb-4">
          <Shimmer className="h-4 w-full mb-2" />
          <Shimmer className="h-4 w-5/6 mb-2" />
          <Shimmer className="h-4 w-3/4" />
        </div>

        {/* Divider */}
        <div className="border-t border-gray-200 my-4"></div>

        {/* Stats */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <Shimmer className="h-4 w-20" />
            <Shimmer className="h-4 w-8" />
          </div>
          <div className="flex justify-between items-center">
            <Shimmer className="h-4 w-24" />
            <Shimmer className="h-4 w-6" />
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-200 my-4"></div>

        {/* Network */}
        <div className="mb-4">
          <Shimmer className="h-4 w-28 mb-2" />
          <div className="flex items-center">
            <Shimmer className="w-4 h-4 rounded-sm mr-2" />
            <Shimmer className="h-4 w-32" />
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-200 my-4"></div>

        {/* Quick Links */}
        <div className="space-y-2">
          <div className="flex items-center">
            <Shimmer className="w-4 h-4 mr-2" />
            <Shimmer className="h-4 w-20" />
          </div>
          <div className="flex items-center">
            <Shimmer className="w-4 h-4 mr-2" />
            <Shimmer className="h-4 w-16" />
          </div>
          <div className="flex items-center">
            <Shimmer className="w-4 h-4 mr-2" />
            <Shimmer className="h-4 w-14" />
          </div>
        </div>
      </div>
    </div>
  );
};

export const MobileAppPromotionSkeleton = () => (
  <div className="bg-white rounded-lg border border-gray-200 p-6">
    <Shimmer className="h-6 w-32 mb-4" />
    <Shimmer className="h-32 w-full rounded-lg mb-4" />
    <Shimmer className="h-4 w-full mb-2" />
    <Shimmer className="h-4 w-3/4 mb-4" />
    <Shimmer className="h-10 w-full rounded-lg" />
  </div>
);

export default Shimmer;
