import useAuth from '@/hooks/useAuth';
import { UserI } from '@/networks/forum/types';

const mapUser = (user: UserI | null) => {
  return user
    ? {
        id: user.id,
        name: user.name,
        email: user.email,
        avatar: user.avatar ?? null,
        title: user.title ?? '',
        company: user.company ?? '',
        location: user.location,
        isVerified: user.isVerified,
        bio: user.bio,
      }
    : null;
};

const useForums = () => {
  const { user } = useAuth();

  const mappedUser = mapUser(user);

  return {
    user: mappedUser,
  };
};
export default useForums;
