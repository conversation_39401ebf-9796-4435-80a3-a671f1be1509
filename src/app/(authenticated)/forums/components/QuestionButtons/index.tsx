import UpIcon from '@assets/svg/Up';
import DownIcon from '@assets/svg/Down';
import BulbIcon from '@assets/svg/Bulb';
import { QuestionButtonsPropsI } from './types';
import { ApiQuestionI } from '@/networks/forum/types';

const QuestionButtons = ({
  question,
  onUpvote,
  onDownvote,
  onAnswer,
}: QuestionButtonsPropsI) => {
  // Helper function to check if it's an API question
  const isApiQuestion = (q: any): q is ApiQuestionI => {
    return 'upvoteCount' in q;
  };

  // Helper function to format numbers (e.g., 1100 -> 1.1K)
  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return (count / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
    }
    if (count >= 1000) {
      return (count / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
    }
    return count.toString();
  };

  // Get counts from question data
  const getUpvoteCount = (): number => {
    if (isApiQuestion(question)) {
      return question.upvoteCount || 0;
    }
    return question.upvotes || 0;
  };

  const getDownvoteCount = (): number => {
    if (isApiQuestion(question)) {
      return question.downvoteCount || 0;
    }
    return question.downvotes || 0;
  };

  const getAnswerCount = (): number => {
    if (isApiQuestion(question)) {
      return question.answerCount || 0;
    }
    return question.answerCount || 0;
  };

  const upvoteCount = getUpvoteCount();
  const downvoteCount = getDownvoteCount();
  const answerCount = getAnswerCount();

  const handleUpvote = () => {
    onUpvote?.(question.id);
  };

  const handleDownvote = () => {
    onDownvote?.(question.id);
  };

  const handleAnswer = () => {
    onAnswer?.(question.id);
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-2">
        <div className="flex items-center space-x-1">
          <button
            onClick={handleUpvote}
            className="flex items-center space-x-1 px-3 py-2 text-xs font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-full transition-colors duration-200"
          >
            <UpIcon size={16} />
            <span>{formatCount(upvoteCount)}</span>
          </button>

          <button
            onClick={handleDownvote}
            className="flex items-center space-x-1 px-3 py-2 text-xs font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-full transition-colors duration-200"
          >
            <DownIcon size={16} color="#B91C1C" />
            <span>{formatCount(downvoteCount)}</span>
          </button>
        </div>

        <button
          onClick={handleAnswer}
          className="flex items-center space-x-1 px-4 py-2 text-xs font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-full transition-colors duration-200"
        >
          <BulbIcon size={16} color="#FACC15" />
          <span>{formatCount(answerCount)}</span>
        </button>
      </div>
    </div>
  );
};

export default QuestionButtons;
