import UpIcon from '@assets/svg/Up';
import DownIcon from '@assets/svg/Down';
import BulbIcon from '@assets/svg/Bulb';
import { QuestionButtonsPropsI } from './types';

const QuestionButtons = ({
  id,
  upvoteCount,
  downvoteCount,
  answerCount,
  currentVote,
  onUpvote,
  onDownvote,
  onAnswer,
  className = '',
  size = 'md',
  variant = 'default',
}: QuestionButtonsPropsI) => {
  // Helper function to format numbers (e.g., 1100 -> 1.1K)
  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return (count / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
    }
    if (count >= 1000) {
      return (count / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
    }
    return count.toString();
  };

  // Get size-based classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          button: 'px-2 py-1 text-xs',
          icon: 12,
        };
      case 'lg':
        return {
          button: 'px-4 py-3 text-sm',
          icon: 20,
        };
      default: // md
        return {
          button: 'px-3 py-2 text-xs',
          icon: 16,
        };
    }
  };

  // Get variant-based classes
  const getVariantClasses = () => {
    switch (variant) {
      case 'compact':
        return 'space-x-1';
      case 'minimal':
        return 'space-x-0.5';
      default:
        return 'space-x-2';
    }
  };

  const sizeClasses = getSizeClasses();
  const variantClasses = getVariantClasses();

  const handleUpvote = () => {
    onUpvote?.(id);
  };

  const handleDownvote = () => {
    onDownvote?.(id);
  };

  const handleAnswer = () => {
    onAnswer?.(id);
  };

  return (
    <div className={`flex items-center justify-between ${className}`}>
      <div className={`flex items-center ${variantClasses}`}>
        <div className="flex items-center space-x-1">
          <button
            onClick={handleUpvote}
            className={`flex items-center space-x-1 ${sizeClasses.button} font-medium rounded-full transition-colors duration-200 ${
              currentVote === 'UP'
                ? 'text-green-700 bg-green-100 hover:bg-green-200'
                : 'text-gray-700 bg-gray-50 hover:bg-gray-100'
            }`}
          >
            <UpIcon size={sizeClasses.icon} />
            <span>{formatCount(upvoteCount)}</span>
          </button>

          <button
            onClick={handleDownvote}
            className={`flex items-center space-x-1 ${sizeClasses.button} font-medium rounded-full transition-colors duration-200 ${
              currentVote === 'DOWN'
                ? 'text-red-700 bg-red-100 hover:bg-red-200'
                : 'text-gray-700 bg-gray-50 hover:bg-gray-100'
            }`}
          >
            <DownIcon
              size={sizeClasses.icon}
              color={currentVote === 'DOWN' ? '#B91C1C' : '#6B7280'}
            />
            <span>{formatCount(downvoteCount)}</span>
          </button>
        </div>

        <button
          onClick={handleAnswer}
          className={`flex items-center space-x-1 ${sizeClasses.button} font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-full transition-colors duration-200`}
        >
          <BulbIcon size={sizeClasses.icon} color="#FACC15" />
          <span>{formatCount(answerCount)}</span>
        </button>
      </div>
    </div>
  );
};

export default QuestionButtons;
