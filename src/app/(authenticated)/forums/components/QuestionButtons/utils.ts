import {
  QuestionI,
  ApiQuestionI,
  QuestionDetailI,
} from '@/networks/forum/types';

export type QuestionDataI = {
  id: string;
  upvoteCount: number;
  downvoteCount: number;
  answerCount: number;
  currentVote?: 'UP' | 'DOWN' | null;
};

/**
 * Helper function to check if it's an API question
 */
const isApiQuestion = (question: any): question is ApiQuestionI => {
  return 'upvoteCount' in question;
};

/**
 * Helper function to check if it's a detailed question
 */
const isDetailedQuestion = (question: any): question is QuestionDetailI => {
  return 'vote' in question;
};

/**
 * Extract question button data from any question type
 */
export const extractQuestionButtonData = (
  question: QuestionI | ApiQuestionI | QuestionDetailI
): QuestionDataI => {
  // Handle QuestionDetailI (from question detail page)
  if (isDetailedQuestion(question)) {
    return {
      id: question.id,
      upvoteCount: question.upvoteCount || 0,
      downvoteCount: question.downvoteCount || 0,
      answerCount: question.answerCount || 0,
      currentVote: question.vote || null,
    };
  }

  // Handle ApiQuestionI (from API)
  if (isApiQuestion(question)) {
    return {
      id: question.id,
      upvoteCount: question.upvoteCount || 0,
      downvoteCount: question.downvoteCount || 0,
      answerCount: question.answerCount || 0,
      currentVote: null, // API questions don't have vote info
    };
  }

  // Handle legacy QuestionI
  return {
    id: question.id,
    upvoteCount: question.upvotes || 0,
    downvoteCount: question.downvotes || 0,
    answerCount: question.answerCount || 0,
    currentVote: null, // Legacy questions don't have vote info
  };
};
