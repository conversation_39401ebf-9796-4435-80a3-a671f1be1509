'use client'
import React from 'react'
import Question<PERSON><PERSON> from '../QuestionCard'
import { QuestionCardSkeleton } from '../Shimmer'
import useForumQuestions from './useHook'

const ForumFeed = () => {
    const { questions, loading, error } = useForumQuestions()
    return (
        <div className="space-y-4">
            {questions.map(question => (
                <QuestionCard
                    key={question.id}
                    question={question}
                    onAnswer={handleAnswer}
                    onFollow={handleFollow}
                    onPass={handlePass}
                    isAuthenticated={isAuthenticated}
                />
            ))}

            {loading && (
                <div className="space-y-4">
                    {Array.from({ length: 2 }).map((_, index) => (
                        <QuestionCardSkeleton key={`loading-${index}`} />
                    ))}
                </div>
            )}

                {hasMoreQuestions && !loading && onLoadMore && (
                    <div className="text-center py-6">
                    <button
                        onClick={loadMore}
                        className="inline-flex items-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        Load More Questions
                    </button>
                </div>
            )}

            {!hasMoreQuestions && questions.length > 0 && (
                <div className="text-center py-6">
                    <p className="text-sm text-gray-500">
                        You've reached the end of the feed
                    </p>
                </div>
            )}

            {!loading && !hasMoreQuestions && error &&  (
                <div className="text-center py-6">
                    <p className="text-sm text-red-600 mb-2">
                        Failed to load more questions
                    </p>
                    <button
                        onClick={loadMore}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                        Try Again
                    </button>
                </div>
            )}
        </div>
    )
}

export default ForumFeed