'use client'
import { fetchQuestionsListingAPI } from "@/networks/forum/globalSearch"
import { ForumQuestionListingI } from "@/networks/forum/types"
import { useEffect, useState } from "react"

const useForumQuestions = () => {
    const [questions, setQuestions] = useState<ForumQuestionListingI[]>([])
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<unknown>()
    const [hasMoreQuestions, setHasMoreQuestions] = useState(true);

    const fetchQuestions = async () => {
        try {
            setLoading(true);
            const response = await fetchQuestionsListingAPI({
                cursorDate: null,
                pageSize: 10,
                type: 'ALL',
            })
            setQuestions(response.data)
            setHasMoreQuestions(!!response.nextCursorDate)
        } catch(err) {
            setError(err)
            console.log(err, 'error')
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchQuestions()
    }, [])

    return {
        questions,
        loading,
        error
    }
}
export default useForumQuestions