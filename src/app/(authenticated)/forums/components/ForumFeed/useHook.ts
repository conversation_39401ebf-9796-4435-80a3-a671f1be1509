'use client'
import { fetchQuestionsListingAPI } from "@/networks/forum/globalSearch"
import { ApiQuestionI } from "@/networks/forum/types"
import { useEffect, useState, useCallback } from "react"

const useForumQuestions = () => {
    const [questions, setQuestions] = useState<ApiQuestionI[]>([])
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [hasMoreQuestions, setHasMoreQuestions] = useState(true);
    const [nextCursorDate, setNextCursorDate] = useState<Date | null>(null);

    const fetchQuestions = useCallback(async (isLoadMore = false) => {
        try {
            if (isLoadMore) {
                setLoadingMore(true);
            } else {
                setLoading(true);
            }
            setError(null);

            const response = await fetchQuestionsListingAPI({
                cursorDate: isLoadMore ? nextCursorDate : null,
                pageSize: 10,
                type: 'ALL',
            });

            const convertedQuestions = response.data;

            if (isLoadMore) {
                setQuestions(prev => [...prev, ...convertedQuestions]);
            } else {
                setQuestions(convertedQuestions);
            }

            setNextCursorDate(response.nextCursorDate);
            setHasMoreQuestions(!!response.nextCursorDate);
        } catch(err) {
            setError(err instanceof Error ? err.message : 'Failed to load questions');
            console.error('Error fetching questions:', err);
        } finally {
            setLoading(false);
            setLoadingMore(false);
        }
    }, [nextCursorDate]);

    const loadMore = useCallback(() => {
        if (!loadingMore && hasMoreQuestions && nextCursorDate) {
            fetchQuestions(true);
        }
    }, [loadingMore, hasMoreQuestions, nextCursorDate]);

    const retry = useCallback(() => {
        if (questions.length === 0) {
            fetchQuestions(false);
        } else {
            loadMore();
        }
    }, [questions.length, loadMore]);

    const refresh = useCallback(() => {
        setQuestions([]);
        setNextCursorDate(null);
        setHasMoreQuestions(true);
        setError(null);
        // fetchQuestions(false);
    }, [fetchQuestions]);

    useEffect(() => {
        fetchQuestions(false);
    }, []);

    return {
        questions,
        loading,
        loadingMore,
        error,
        hasMoreQuestions,
        loadMore,
        retry,
        refresh
    }
}

export default useForumQuestions