'use client';

import Image from 'next/image';
import { AnswerCardPropsI } from './types';
import { MediaFileI } from '@/networks/forum/types';
import UpIcon from '@assets/svg/Up';
import DownIcon from '@assets/svg/Down';
import QuestionImageViewer from '@/app/(authenticated)/forums/components/QuestionImageViewer';
import QuestionAttachments from '@/app/(authenticated)/forums/components/QuestionAttachments';

const AnswerCard = ({ answer, onUpvote, onDownvote }: AnswerCardPropsI) => {
  const getMediaFiles = (): MediaFileI[] => {
    if (!answer.media) return [];
    return answer.media.map(media => ({
      id: media.id,
      url: media.fileUrl,
      name: `file.${media.fileExtension}`,
      type: getMediaType(media.fileExtension),
      mimeType: getMimeType(media.fileExtension),
    }));
  };

  const getMediaType = (extension: string): MediaFileI['type'] => {
    const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv'];
    const audioExts = ['mp3', 'wav', 'ogg', 'aac'];

    if (imageExts.includes(extension.toLowerCase())) return 'image';
    if (videoExts.includes(extension.toLowerCase())) return 'video';
    if (audioExts.includes(extension.toLowerCase())) return 'audio';
    if (extension.toLowerCase() === 'pdf') return 'pdf';
    if (['doc', 'docx'].includes(extension.toLowerCase())) return 'doc';
    if (['xls', 'xlsx'].includes(extension.toLowerCase())) return 'sheet';
    return 'other';
  };

  const getMimeType = (extension: string): string => {
    const mimeTypes: Record<string, string> = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      webp: 'image/webp',
      svg: 'image/svg+xml',
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      xls: 'application/vnd.ms-excel',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      mp4: 'video/mp4',
      mp3: 'audio/mpeg',
    };
    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
  };

  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return (count / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
    }
    if (count >= 1000) {
      return (count / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
    }
    return count.toString();
  };

  const mediaFiles = getMediaFiles();
  const imageFiles = mediaFiles
    .filter(file => file.type === 'image')
    ?.map(item => item.url);
  const nonImageFiles = mediaFiles.filter(file => file.type !== 'image');

  const handleUpvote = () => {
    onUpvote?.(answer.id);
  };

  const handleDownvote = () => {
    onDownvote?.(answer.id);
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
      {/* Verified Badge */}
      {answer.status === 'VERIFIED_SOLUTION' && (
        <div className="mb-4 flex justify-end">
          <span className="inline-flex items-center  px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
            ✓ Verified Answer
          </span>
        </div>
      )}

      {/* Author Info */}
      <div className="flex items-start space-x-3 mb-4">
        {answer.profile.avatar ? (
          <Image
            src={answer.profile.avatar}
            alt={answer.profile.name}
            width={40}
            height={40}
            className="rounded-full"
          />
        ) : (
          <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
            <span className="text-gray-600 text-sm font-medium">
              {answer.profile.name.charAt(0).toUpperCase()}
            </span>
          </div>
        )}

        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <span className="font-medium text-gray-900">
              {answer.profile.name}
            </span>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {new Date(answer.createdAt).toLocaleDateString()}
          </div>
        </div>
      </div>

      {/* Answer Content */}
      <div className="mb-4">
        <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
          {answer.text}
        </p>
      </div>

      {/* Media Files */}
      {mediaFiles.length > 0 && (
        <>
          <QuestionImageViewer allImages={imageFiles} />
          <QuestionAttachments nonImageFiles={nonImageFiles} />
        </>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-100">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleUpvote}
            className="flex items-center space-x-1 px-3 py-1.5 text-sm text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-md transition-colors"
          >
            <UpIcon size={16} />
            <span>{formatCount(answer.upvoteCount)}</span>
          </button>

          <button
            onClick={handleDownvote}
            className="flex items-center space-x-1 px-3 py-1.5 text-sm text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
          >
            <DownIcon size={16} />
            <span>{formatCount(answer.downvoteCount)}</span>
          </button>
        </div>

        <div className="text-xs text-gray-500">
          {new Date(answer.createdAt).toLocaleString()}
        </div>
      </div>
    </div>
  );
};

export default AnswerCard;
