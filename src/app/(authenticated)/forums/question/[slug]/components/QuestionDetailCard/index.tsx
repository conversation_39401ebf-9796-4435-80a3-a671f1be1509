'use client';

import Image from 'next/image';
import { MediaFileI } from '@/networks/forum/types';
import UpIcon from '@assets/svg/Up';
import DownIcon from '@assets/svg/Down';
import BulbIcon from '@assets/svg/Bulb';
import QuestionImageViewer from '@/app/(authenticated)/forums/components/QuestionImageViewer';
import QuestionAttachments from '@/app/(authenticated)/forums/components/QuestionAttachments';
import { QuestionDetailCardPropsI } from './type';



const QuestionDetailCard = ({ 
  question, 
  onUpvote, 
  onDownvote, 
  onAnswer 
}: QuestionDetailCardPropsI) => {
  
  const getMediaFiles = (): MediaFileI[] => {
    if (!question.media) return [];
    return question.media.map(media => ({
      id: media.id,
      url: media.fileUrl,
      name: `file.${media.fileExtension}`,
      type: getMediaType(media.fileExtension),
      mimeType: getMimeType(media.fileExtension),
    }));
  };

  const getMediaType = (extension: string): MediaFileI['type'] => {
    const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv'];
    const audioExts = ['mp3', 'wav', 'ogg', 'aac'];

    if (imageExts.includes(extension.toLowerCase())) return 'image';
    if (videoExts.includes(extension.toLowerCase())) return 'video';
    if (audioExts.includes(extension.toLowerCase())) return 'audio';
    if (extension.toLowerCase() === 'pdf') return 'pdf';
    if (['doc', 'docx'].includes(extension.toLowerCase())) return 'doc';
    if (['xls', 'xlsx'].includes(extension.toLowerCase())) return 'sheet';
    return 'other';
  };

  const getMimeType = (extension: string): string => {
    const mimeTypes: Record<string, string> = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      webp: 'image/webp',
      svg: 'image/svg+xml',
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      xls: 'application/vnd.ms-excel',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      mp4: 'video/mp4',
      mp3: 'audio/mpeg',
    };
    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
  };

  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return (count / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
    }
    if (count >= 1000) {
      return (count / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
    }
    return count.toString();
  };

  const getTags = (): string[] => {
    const tags: string[] = [];
    
    if (question.topics && question.topics.length > 0) {
      tags.push(...question.topics.map(topic => topic.name));
    }
    
    if (question.equipmentCategory) {
      tags.push(question.equipmentCategory.name);
    }
    if (question.equipmentManufacturer) {
      tags.push(question.equipmentManufacturer.name);
    }
    if (question.equipmentModel) {
      tags.push(question.equipmentModel.name);
    }
    
    return tags;
  };

  const mediaFiles = getMediaFiles();
  const imageFiles = mediaFiles.filter(file => file.type === 'image').map((item) => item.url);
  const nonImageFiles = mediaFiles.filter(file => file.type !== 'image');
  const tags = getTags();
  
  const handleUpvote = () => {
    onUpvote?.(question.id);
  };

  const handleDownvote = () => {
    onDownvote?.(question.id);
  };

  const handleAnswer = () => {
    onAnswer?.(question.id);
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-1.5 hover:shadow-md transition-shadow duration-200 relative">
      {question.isSolved && (
        <div className="absolute top-4 right-4 z-10">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
            ✓ Solved
          </span>
        </div>
      )}

      {tags && tags.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4 border-gray-100">
          {tags.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 hover:bg-blue-100 cursor-pointer transition-colors duration-200"
            >
              {tag}
            </span>
          ))}
        </div>
      )}

      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h1 className="text-2xl font-semibold text-gray-900 leading-tight mb-4">
            {question.title}
          </h1>
        </div>
      </div>

      {question.description && (
        <div className="mb-4 relative">
          <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
            {question.description}
          </p>
        </div>
      )}

      {/* Media Files */}
      {mediaFiles.length > 0 && (
        <>
          <QuestionImageViewer allImages={imageFiles} />
          <QuestionAttachments nonImageFiles={nonImageFiles} />
        </>
      )}

      {/* Author and Stats */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-100">
        <div className="flex items-center space-x-3">
          {question.profile ? (
            <>
              {question.profile.avatar ? (
                <Image
                  src={question.profile.avatar}
                  alt={question.profile.name}
                  width={32}
                  height={32}
                  className="rounded-full"
                />
              ) : (
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 text-sm font-medium">
                    {question.profile.name.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
              <div>
                <span className="text-sm font-medium text-gray-900">
                  {question.profile.name}
                </span>
                <div className="text-xs text-gray-500">
                  {new Date(question.createdAt).toLocaleDateString()}
                </div>
              </div>
            </>
          ) : (
            <div>
              <span className="text-sm font-medium text-gray-900">
                Anonymous
              </span>
              <div className="text-xs text-gray-500">
                {new Date(question.createdAt).toLocaleDateString()}
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <button
              onClick={handleUpvote}
              className={`flex items-center space-x-1 px-3 py-2 text-xs font-medium rounded-full transition-colors duration-200 ${
                question.vote === 'UP' 
                  ? 'text-green-700 bg-green-100' 
                  : 'text-gray-700 bg-gray-50 hover:bg-gray-100'
              }`}
            >
              <UpIcon size={16} />
              <span>{formatCount(question.upvoteCount)}</span>
            </button>
            
            <button
              onClick={handleDownvote}
              className={`flex items-center space-x-1 px-3 py-2 text-xs font-medium rounded-full transition-colors duration-200 ${
                question.vote === 'DOWN' 
                  ? 'text-red-700 bg-red-100' 
                  : 'text-gray-700 bg-gray-50 hover:bg-gray-100'
              }`}
            >
              <DownIcon size={16} color="#B91C1C" />
              <span>{formatCount(question.downvoteCount)}</span>
            </button>
          </div>

          <button
            onClick={handleAnswer}
            className="flex items-center space-x-1 px-4 py-2 text-xs font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-full transition-colors duration-200"
          >
            <BulbIcon size={16} color="#FACC15" />
            <span>{formatCount(question.answerCount)}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuestionDetailCard;
