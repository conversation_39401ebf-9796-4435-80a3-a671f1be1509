'use client';

import Image from 'next/image';
import { MediaFileI } from '@/networks/forum/types';

import Question<PERSON><PERSON>Viewer from '@/app/(authenticated)/forums/components/QuestionImageViewer';
import QuestionAttachments from '@/app/(authenticated)/forums/components/QuestionAttachments';
import { QuestionDetailCardPropsI } from './type';
import QuestionButtons from '@/app/(authenticated)/forums/components/QuestionButtons';
import { extractQuestionButtonData } from '@/app/(authenticated)/forums/components/QuestionButtons/utils';

const QuestionDetailCard = ({
  question,
  onUpvote,
  onDownvote,
  onAnswer,
}: QuestionDetailCardPropsI) => {
  const getMediaFiles = (): MediaFileI[] => {
    if (!question.media) return [];
    return question.media.map(media => ({
      id: media.id,
      url: media.fileUrl,
      name: `file.${media.fileExtension}`,
      type: getMediaType(media.fileExtension),
      mimeType: getMimeType(media.fileExtension),
    }));
  };

  const getMediaType = (extension: string): MediaFileI['type'] => {
    const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv'];
    const audioExts = ['mp3', 'wav', 'ogg', 'aac'];

    if (imageExts.includes(extension.toLowerCase())) return 'image';
    if (videoExts.includes(extension.toLowerCase())) return 'video';
    if (audioExts.includes(extension.toLowerCase())) return 'audio';
    if (extension.toLowerCase() === 'pdf') return 'pdf';
    if (['doc', 'docx'].includes(extension.toLowerCase())) return 'doc';
    if (['xls', 'xlsx'].includes(extension.toLowerCase())) return 'sheet';
    return 'other';
  };

  const getMimeType = (extension: string): string => {
    const mimeTypes: Record<string, string> = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      webp: 'image/webp',
      svg: 'image/svg+xml',
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      xls: 'application/vnd.ms-excel',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      mp4: 'video/mp4',
      mp3: 'audio/mpeg',
    };
    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
  };

  const getTags = (): string[] => {
    const tags: string[] = [];

    if (question.topics && question.topics.length > 0) {
      tags.push(...question.topics.map(topic => topic.name));
    }

    if (question.equipmentCategory) {
      tags.push(question.equipmentCategory.name);
    }
    if (question.equipmentManufacturer) {
      tags.push(question.equipmentManufacturer.name);
    }
    if (question.equipmentModel) {
      tags.push(question.equipmentModel.name);
    }

    return tags;
  };

  const mediaFiles = getMediaFiles();
  const imageFiles = mediaFiles
    .filter(file => file.type === 'image')
    .map(item => item.url);
  const nonImageFiles = mediaFiles.filter(file => file.type !== 'image');
  const tags = getTags();

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-1.5 hover:shadow-md transition-shadow duration-200 relative">
      {question.isSolved && (
        <div className="absolute top-4 right-4 z-10">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
            ✓ Solved
          </span>
        </div>
      )}

      {tags && tags.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4 border-gray-100">
          {tags.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#DDEFC8B2] text-[#737373] hover:bg-blue-100 cursor-pointer transition-colors duration-200"
            >
              {tag}
            </span>
          ))}
        </div>
      )}

      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h1 className="text-2xl font-semibold text-gray-900 leading-tight mb-4">
            {question.title}
          </h1>
        </div>
      </div>

      {question.description && (
        <div className="mb-4 relative">
          <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
            {question.description}
          </p>
        </div>
      )}

      {/* Media Files */}
      {mediaFiles.length > 0 && (
        <>
          <QuestionImageViewer allImages={imageFiles} />
          <QuestionAttachments nonImageFiles={nonImageFiles} />
        </>
      )}

      {/* Author and Stats */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-100">
        <div className="flex items-center space-x-3">
          {question.profile ? (
            <>
              {question.profile.avatar ? (
                <Image
                  src={question.profile.avatar}
                  alt={question.profile.name}
                  width={32}
                  height={32}
                  className="rounded-full"
                />
              ) : (
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 text-sm font-medium">
                    {question.profile.name.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
              <div>
                <span className="text-sm font-medium text-gray-900">
                  {question.profile.name}
                </span>
                <div className="text-xs text-gray-500">
                  {new Date(question.createdAt).toLocaleDateString()}
                </div>
              </div>
            </>
          ) : (
            <div>
              <span className="text-sm font-medium text-gray-900">
                Anonymous
              </span>
              <div className="text-xs text-gray-500">
                {new Date(question.createdAt).toLocaleDateString()}
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <QuestionButtons
          {...extractQuestionButtonData(question)}
          onUpvote={onUpvote}
          onDownvote={onDownvote}
          onAnswer={onAnswer}
          size="md"
          variant="default"
        />
      </div>
    </div>
  );
};

export default QuestionDetailCard;
