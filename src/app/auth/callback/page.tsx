'use client';

import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

const AuthCallbackPage = () => {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return;

    if (status === 'unauthenticated') {
      // If not authenticated, redirect to login
      router.push('/login');
      return;
    }

    if (status === 'authenticated' && session) {
      // Navigate based on user status
      const userStatus = {
        isEmailVerified: (session as any).isEmailVerified || false,
        isUsernameSaved: (session as any).isUsernameSaved || false,
        isPersonalDetailsSaved:
          (session as any).isPersonalDetailsSaved || false,
        isWorkDetailsSaved: (session as any).isWorkDetailsSaved || false,
      };

      // Determine redirect path based on user status
      let redirectPath = '/forums'; // Default to forums

      if (!userStatus.isEmailVerified) {
        redirectPath = '/otp';
      } else if (
        !userStatus.isUsernameSaved ||
        !userStatus.isPersonalDetailsSaved ||
        !userStatus.isWorkDetailsSaved
      ) {
        redirectPath = '/fill-user-details?step=0';
      }

      // Redirect to the appropriate page
      router.push(redirectPath);
    }
  }, [session, status, router]);

  // Show loading state
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white">
      <div className="mb-8">
        <div className="text-2xl font-bold text-[#448600]">Navicater</div>
      </div>

      <div className="flex flex-col items-center space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#448600]"></div>

        <p className="text-gray-600 text-sm font-medium">
          Completing sign in...
        </p>
      </div>
    </div>
  );
};

export default AuthCallbackPage;
