'use server';

import { getServerSession } from 'next-auth/next';
import { signIn } from '../../../../../../auth';

export async function handleGoogleLogin() {
 const session = await getServerSession(authOptions);
  await signIn('google', { redirectTo: session?.isPersonalDetailsSaved ? '/fill-user-details?step=1' : "/" });
}

export async function handleAppleLogin() {
  await signIn('apple', { redirectTo: '/' });
}
