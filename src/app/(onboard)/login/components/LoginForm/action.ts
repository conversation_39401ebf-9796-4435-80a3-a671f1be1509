'use server';

import { signIn } from '../../../../../../auth';

export const handleCredentialLogin = async ({
  email,
  password,
}: {
  email: string;
  password: string;
}) => {
  try {
    const response = await signIn('credentials', {
      type: 'EMAIL_PASSWORD',
      email,
      password,
      redirect: false,
    });

    return response;
  } catch (err) {
    console.log(err, 'LoginErrorMessage');
  }
};
