'use client';

import dynamic from 'next/dynamic';
import { useSearchParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

const UsernameStep = dynamic(() => import('./components/UsernameStep'), {
  ssr: false,
});
const PersonalDetailsStep = dynamic(
  () => import('./components/PersonalDetailsStep'),
  {
    ssr: false,
  }
);
const WorkDetailsStep = dynamic(() => import('./components/WorkDetailsStep'), {
  ssr: false,
});

function FillUserDetailsPage() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const stepParam = parseInt(searchParams.get('step') || '0', 10);
  const [currentStep, setCurrentStep] = useState(stepParam);

  useEffect(() => {
    setCurrentStep(stepParam);
  }, [stepParam]);

  const updateStepInURL = (step: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('step', step.toString());
    router.push(`?${params.toString()}`);
  };

  const handleNextStep = () => {
    updateStepInURL(currentStep + 1);
  };

  const handlePrevStep = () => {
    updateStepInURL(currentStep - 1);
  };

  return (
    <div className="min-h-full bg-white flex flex-col">
      <div className="flex-1 flex flex-col justify-center py-6 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto w-full max-w-md lg:shadow-lg lg:p-8 rounded-sm">
          {currentStep === 0 && <UsernameStep onNext={handleNextStep} />}

          {currentStep === 1 && (
            <PersonalDetailsStep
              onNext={handleNextStep}
              onBack={handlePrevStep}
            />
          )}

          {currentStep === 2 && <WorkDetailsStep onBack={handlePrevStep} />}
        </div>
      </div>
    </div>
  );
}

export default FillUserDetailsPage;
