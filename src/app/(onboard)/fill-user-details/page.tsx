'use client';

import { useState } from 'react';
import dynamic from 'next/dynamic'

const UsernameStep = dynamic(() => import('./components/UsernameStep'), { ssr: false })
const PersonalDetailsStep = dynamic(() => import('./components/PersonalDetailsStep'), { ssr: false })
const WorkDetailsStep = dynamic(() => import('./components/WorkDetailsStep'), { ssr: false })

function FillUserDetailsPage() {
  const [currentStep, setCurrentStep] = useState(0);

  const handleNextStep = () => {
    setCurrentStep(prev => prev + 1);
  };

  const handlePrevStep = () => {
    setCurrentStep(prev => prev - 1);
  };

  return (
    <div className="min-h-full bg-white flex flex-col">
      <div className="flex-1 flex flex-col justify-center py-6 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto w-full max-w-md lg:shadow-lg lg:p-8 rounded-sm">
          {currentStep === 0 && <UsernameStep onNext={handleNextStep} />}

          {currentStep === 1 && (
            <PersonalDetailsStep
              onNext={handleNextStep}
              onBack={handlePrevStep}
            />
          )}

          {currentStep === 2 && <WorkDetailsStep onBack={handlePrevStep} />}
        </div>
      </div>
    </div>
  );
}

export default FillUserDetailsPage;
