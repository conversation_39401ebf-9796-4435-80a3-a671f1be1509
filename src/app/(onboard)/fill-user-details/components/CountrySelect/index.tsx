'use client';

import React from 'react';
import { Controller } from 'react-hook-form';
import { Select } from '@/components';
import { CountrySelectPropsI } from './types';
import useCountries from './useHook';

const CountrySelect = ({
    control,
    name = 'countryIso2',
    isRequired = false,
    disabled = false,
    label = 'Country',
    placeholder = 'Select your country',
    className = 'w-full'
}: CountrySelectPropsI) => {

    const { countries, loading, loadMore, hasMore, onSearch } = useCountries()
    return (
        <Controller
            control={control}
            name={name}
            rules={{
                required: isRequired ? 'Please select a country' : false,
            }}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
                <Select
                    label={label}
                    placeholder={placeholder}
                    options={(countries) ?? []}
                    value={value}
                    onValueChange={onChange}
                    onSearch={onSearch}
                    error={error?.message}
                    disabled={disabled}
                    className={className}
                    loading={loading}
                    onLoadMore={loadMore}
                    hasMore={hasMore}
                    searchable={true}
                    optionLabelKey='name'
                    optionValueKey='iso2'
                    searchPlaceholder="Search countries..."
                />
            )}
        />
    );
};

export default CountrySelect;