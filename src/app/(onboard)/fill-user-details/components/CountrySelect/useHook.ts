import { fetchCountries } from "@/networks/data/country";
import { CountriesI, FetchCountryResponseI } from "@/networks/data/types";
import { useEffect, useState } from "react";

const useCountries = () => {
    const [countries, setCountries] = useState<CountriesI[]>([])
    const [hasMore, setHasMore] = useState(false);
    const [loading, setLoading] = useState(false)
    const [pageNum, setPageNum] = useState(1)

    const fetchCountriesAsync = async ({ page = 1, search }: { page: number, search?: string }): Promise<FetchCountryResponseI | void> => {
        try {
            setLoading(true)
            const params = { page: String(page) };
            const result = await fetchCountries(params)
            console.log(result, page, 'pageResult')
            if (page === 1) {
                setCountries(result.data);
            } else {
                setCountries(prevCountries => [...prevCountries, ...result.data]);
            }
            
            const totalLoadedAfterThis = page === 1 ? result.data.length : countries.length + result.data.length;
            setHasMore(result.total > totalLoadedAfterThis);
            
            setPage(page)
            return result;
        } catch (error) {
            console.error('Error fetching countries:', error);
        } finally {
            setLoading(false)
        }
    };

    const loadMore = () => {
        
        if (hasMore && !loading) {
            fetchCountriesAsync({
                page: page + 1,
            })
        }
    }

    const reset = () => {
        setCountries([]);
        setPage(1);
        setHasMore(false);
        fetchCountriesAsync({ page: 1 });
    }

    useEffect(() => {
        fetchCountriesAsync({ page: 1 })
    }, [])

    return {
        countries,
        loadMore,
        loading,
        hasMore,
        reset, 
        page, 
    }
}

export default useCountries;