import { fetchCountries } from '@/networks/data/country';
import { CountriesI, FetchCountryResponseI } from '@/networks/data/types';
import { useEffect, useState } from 'react';

const useCountries = () => {
  const [countries, setCountries] = useState<CountriesI[]>([]);
  const [hasMore, setHasMore] = useState(false);
  const [loading, setLoading] = useState(false);
  const [pageNum, setPageNum] = useState(1);
  const [searchTerm, setSearchTerm] = useState<string>('');

  const fetchCountriesAsync = async ({
    page = 1,
    search,
  }: {
    page: number;
    search?: string;
  }): Promise<FetchCountryResponseI | void> => {
    try {
      setLoading(true);
      const params = {
        page: String(page),
        search: search?.trim() || undefined,
      };
      const result = await fetchCountries(params);

      setCountries(prevCountries => {
        const countryMap = new Map<string, CountriesI>();

        // Add existing countries to map
        if (page !== 1) {
          prevCountries.forEach(country => {
            countryMap.set(country.iso2, country);
          });
        }

        // Add new countries to map (will overwrite duplicates)
        result.data.forEach(country => {
          countryMap.set(country.iso2, country);
        });

        const newCountries = Array.from(countryMap.values());
        const totalLoadedAfterThis = newCountries.length;
        console.log(result.total, totalLoadedAfterThis, 'totalCount');
        setHasMore(result.total > totalLoadedAfterThis);
        return newCountries;
      });

      return result;
    } catch (error) {
      console.error('Error fetching countries:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMore = () => {
    if (hasMore && !loading) {
      const nextPage = pageNum + 1;
      setPageNum(nextPage);
      fetchCountriesAsync({
        page: nextPage,
        search: searchTerm,
      });
    }
  };

  const reset = () => {
    setCountries([]);
    setPageNum(1);
    setHasMore(false);
    fetchCountriesAsync({ page: 1 });
  };

  const onSearch = (text: string) => {
    setSearchTerm(text);
    setCountries([]);
    setPageNum(1);
    setHasMore(false);

    fetchCountriesAsync({ page: 1, search: text.trim() || undefined });
  };

  useEffect(() => {
    fetchCountriesAsync({ page: 1 });
  }, []);

  return {
    countries,
    loadMore,
    loading,
    hasMore,
    reset,
    pageNum,
    onSearch,
  };
};

export default useCountries;
