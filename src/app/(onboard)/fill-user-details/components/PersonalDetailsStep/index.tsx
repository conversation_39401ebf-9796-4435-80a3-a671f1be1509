'use client';

import { useForm, Controller } from 'react-hook-form';
import { Button, Input, Select } from '@/components';
import { onboardingPersonalAPI } from '@/networks/user/onboarding';
import CountrySelect from '../CountrySelect';

type PersonalDetailsFormData = {
  fullName: string;
  gender: string;
  countryIso2: string;
};

const genderOptions = [
  { value: 'MALE', label: 'Male' },
  { value: 'FEMALE', label: 'Female' },
  { value: 'OTHER', label: 'Other' },
  { value: 'n', label: 'Prefer Not to Say' },
];

type PersonalDetailsStepProps = {
  onNext: () => void;
  onBack: () => void;
};

const PersonalDetailsStep = ({ onNext, onBack }: PersonalDetailsStepProps) => {
  const userPersonal = localStorage.getItem('userPersonalData');
  const personalData = userPersonal ? JSON.parse(userPersonal) : null;

  const {
    control,
    handleSubmit,
    formState: { isSubmitting },
    setError,
  } = useForm<PersonalDetailsFormData>({
    defaultValues: {
      fullName: personalData?.fullName || '',
      gender: personalData?.gender || '',
      countryIso2: personalData?.countryIso2 || '',
    },
  });

  const onSubmit = async (data: PersonalDetailsFormData) => {
    try {
      await onboardingPersonalAPI({
        fullName: data.fullName,
        gender: data.gender,
        countryIso2: data.countryIso2,
      });
      const userPersonalData = {
        fullName: data.fullName,
        gender: data.gender,
        countryIso2: data.countryIso2,
      };
      localStorage.setItem(
        'userPersonalData',
        JSON.stringify(userPersonalData)
      );
      onNext();
    } catch (error) {
      console.error('Error saving personal details:', error);
      setError('fullName', {
        type: 'manual',
        message:
          error instanceof Error
            ? error.message
            : 'Failed to save personal details. Please try again.',
      });
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl font-semibold text-black mb-2">
          Personal Details
        </h1>
        <p className="text-gray-600">
          Add your personal information to help others connect with you.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Controller
          control={control}
          name="fullName"
          rules={{
            required: 'Full name is required',
            minLength: {
              value: 2,
              message: 'Full name must be at least 2 characters',
            },
            maxLength: {
              value: 50,
              message: 'Full name cannot exceed 50 characters',
            },
          }}
          render={({
            field: { onChange, value, onBlur },
            fieldState: { error },
          }) => (
            <Input
              label="Full Name"
              placeholder="Enter your full name"
              value={value}
              onChange={e => onChange(e.target.value)}
              onBlur={onBlur}
              error={error?.message}
              disabled={isSubmitting}
              className="w-full"
            />
          )}
        />

        <Controller
          control={control}
          name="gender"
          rules={{
            required: 'Please select your gender',
          }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <Select
              label="Gender"
              placeholder="Select your gender"
              options={genderOptions}
              value={value}
              onValueChange={onChange}
              error={error?.message}
              disabled={isSubmitting}
              className="w-full"
            />
          )}
        />

        <CountrySelect
          name="countryIso2"
          label="Country"
          control={control}
          isRequired
          disabled={isSubmitting}
        />

        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            onClick={onBack}
            className="flex-1 rounded-full border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 text-base font-semibold py-3"
            disabled={isSubmitting}
          >
            Back
          </Button>
          <Button
            type="submit"
            className="flex-1 rounded-full bg-[#448600] hover:bg-[#357000] text-white text-base font-semibold py-3"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Continue'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default PersonalDetailsStep;
