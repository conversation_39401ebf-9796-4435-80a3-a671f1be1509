'use client';

import { useForm } from 'react-hook-form';
import type { SearchableOptionI } from '@/components/SearchableSelect';

type WorkDetailsFormData = {
  designation: SearchableOptionI | null;
  entity: SearchableOptionI | null;
};

type WorkDetailsStepProps = {
  onBack: () => void;
};

const WorkDetailsStep = ({ onBack }: WorkDetailsStepProps) => {
  const {
    // control,
    // handleSubmit,
    // formState,
    setError,
  } = useForm<WorkDetailsFormData>({
    defaultValues: {
      designation: null,
      entity: null,
    },
  });

  // const searchDesignations = async (
  //   query: string
  // ): Promise<SearchableOptionI[]> => {
  //   try {
  //     const results = await searchDesignationsAPI(query);
  //     return results;
  //   } catch (error) {
  //     console.error('Error searching designations:', error);
  //     return [];
  //   }
  // };

  // const searchEntities = async (
  //   query: string
  // ): Promise<SearchableOptionI[]> => {
  //   try {
  //     const results = await searchEntitiesAPI(query);
  //     return results;
  //   } catch (error) {
  //     console.error('Error searching entities:', error);
  //     return [];
  //   }
  // };

  // const onSubmit = async (data: WorkDetailsFormData) => {
  //   try {
  //     if (!data.designation || !data.entity) {
  //       throw new Error('Please select both designation and organization');
  //     }

  //     // await onboardingWorkAPI({
  //     //   designation: {
  //     //     id: data.designation.id,
  //     //     dataType: data.designation.dataType,
  //     //   },
  //     //   entity: {
  //     //     id: data.entity.id,
  //     //     dataType: data.entity.dataType,
  //     //   },
  //     // });

  //     // Navigate to main app after completion
  //     window.location.href = '/forums';
  //   } catch (error) {
  //     console.error('Error saving work details:', error);
  //     setError('designation', {
  //       type: 'manual',
  //       message:
  //         error instanceof Error
  //           ? error.message
  //           : 'Failed to save work details. Please try again.',
  //     });
  //   }
  // };

  const handleSkip = () => {
    window.location.href = '/forums';
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl font-semibold text-black mb-2">Work Details</h1>
        <p className="text-gray-600">
          Share your professional background and current role.
        </p>
      </div>

      {/* <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Controller
          control={control}
          name="designation"
          rules={{
            required: 'Please select a designation',
          }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <SearchableSelect
              label="Designation"
              placeholder="Search for your designation..."
              value={value}
              onValueChange={onChange}
              onSearch={searchDesignations}
              error={error?.message}
              disabled={isSubmitting}
              className="w-full"
            />
          )}
        />

        <Controller
          control={control}
          name="entity"
          rules={{
            required: 'Please select an organization',
          }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <SearchableSelect
              label="Organization"
              placeholder="Search for your organization..."
              value={value}
              onValueChange={onChange}
              onSearch={searchEntities}
              error={error?.message}
              disabled={isSubmitting}
              className="w-full"
            />
          )}
        />

        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            onClick={onBack}
            className="flex-1 rounded-full border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 text-base font-semibold py-3"
            disabled={isSubmitting}
          >
            Back
          </Button>
          <Button
            type="submit"
            className="flex-1 rounded-full bg-[#448600] hover:bg-[#357000] text-white text-base font-semibold py-3"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Completing...' : 'Complete'}
          </Button>
        </div>

        <div className="text-center pt-2">
          <button
            type="button"
            onClick={handleSkip}
            className="text-gray-600 hover:text-gray-800 text-sm font-medium underline"
            disabled={isSubmitting}
          >
            Skip for now
          </button>
        </div>
      </form> */}
    </div>
  );
};

export default WorkDetailsStep;
