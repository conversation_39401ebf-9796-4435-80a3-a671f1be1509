'use client';

import { useState } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { Button, Input } from '@/components';
import {
  checkUsernameAPI,
  updateUsernameAPI,
} from '@/networks/user/onboarding';
import APIResError from '@/errors/networks/APIResError';
import { debounce } from '@/utils/debounce';
import { validateUsername } from './schema';

type UsernameFormData = {
  userName: string;
};

type UsernameStepProps = {
  onNext: () => void;
};

const UsernameStep = ({ onNext }: UsernameStepProps) => {
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);
  const [usernameAvailable, setUsernameAvailable] = useState<boolean | null>(
    null
  );
  const userName = typeof window !== 'undefined' ? localStorage.getItem('username') : "";

  const {
    control,
    handleSubmit,
    formState: { isSubmitting },
    setError,
    clearErrors,
  } = useForm<UsernameFormData>({
    defaultValues: {
      userName: userName || '',
    },
  });

  const onUsernameChange = debounce(async (username: string) => {
    if (!username || username.length < 4) {
      setUsernameAvailable(null);
      return;
    }
    setIsCheckingUsername(true);
    try {
      if (userName === username) return;
      await checkUsernameAPI({ username });
      setUsernameAvailable(true);
      clearErrors('userName');
    } catch (error: any) {
      if (error instanceof APIResError && error.status === 409) {
        setUsernameAvailable(false);
        setError('userName', {
          type: 'manual',
          message: 'Username is already taken',
        });
      } else {
        setUsernameAvailable(null);
        setError('userName', {
          type: 'manual',
          message: 'Error checking username',
        });
      }
    } finally {
      setIsCheckingUsername(false);
    }
  }, 500);

  const onSubmit = async (data: UsernameFormData) => {
    try {
      if (userName !== data.userName) {
        await updateUsernameAPI({ username: data.userName });
        localStorage.setItem('username', data.userName);
      }
      onNext();
    } catch (error) {
      console.error('Error saving username:', error);
      setError('userName', {
        type: 'manual',
        message:
          error instanceof Error
            ? error.message
            : 'Failed to save username. Please try again.',
      });
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl font-semibold text-black mb-2">
          Choose Your Username
        </h1>
        <p className="text-gray-600">
          Choose a unique username that represents you professionally.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Controller
          control={control}
          name="userName"
          rules={{
            required: 'Username is required',
            validate: validateUsername,
          }}
          render={({
            field: { onChange, value, onBlur },
            fieldState: { error },
          }) => (
            <div>
              <Input
                label="Username"
                placeholder="Enter username (min 4 characters)"
                value={value}
                onChange={e => {
                  const val = e.target.value;
                  onChange(val);
                  onUsernameChange(val);
                }}
                onBlur={onBlur}
                error={error?.message}
                disabled={isSubmitting}
                className="w-full"
              />
              {isCheckingUsername && (
                <div className="mt-2 flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#448600]"></div>
                  <span className="text-sm text-gray-600">
                    Checking availability...
                  </span>
                </div>
              )}
              {usernameAvailable === true && !error && (
                <div className="mt-2 flex items-center gap-2">
                  <svg
                    className="w-4 h-4 text-green-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="text-sm text-green-600">
                    Username is available!
                  </span>
                </div>
              )}
            </div>
          )}
        />

        <div className="pt-4">
          <Button
            type="submit"
            className="w-full rounded-full bg-[#448600] hover:bg-[#357000] text-white text-base font-semibold py-3"
            disabled={
              isSubmitting || isCheckingUsername || usernameAvailable === false
            }
          >
            {isSubmitting ? 'Saving...' : 'Continue'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default UsernameStep;
