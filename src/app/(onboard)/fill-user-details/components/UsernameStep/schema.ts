export const validateUsername = (value: string) => {
  if (!value) return 'Username is required';
  if (value.length < 4) return 'Username must be at least 4 characters';
  if (value.length > 25) return 'Username cannot exceed 25 characters';
  if (/\s/.test(value)) return 'Username cannot contain spaces';
  if (/^[_]/.test(value)) return 'Cannot start with _';
  if (/^[.]/.test(value)) return 'Cannot start with .';
  if (/[.]$/.test(value)) return 'Cannot end with .';
  if (/[_]$/.test(value)) return 'Cannot end with _';
  if (/[_.]{2}/.test(value))
    return 'Cannot contain consecutive periods/underscores';
  if (/^[._]+$/.test(value))
    return 'Cannot contain only periods or underscores';
  if (!/^[a-zA-Z0-9._]+$/.test(value)) return 'Invalid username format';
  return true;
};
