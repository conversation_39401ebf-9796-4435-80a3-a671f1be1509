import { Suspense } from 'react';
import OtpForm from './components/OtpForm';

const OtpPage = () => {
  return (
    <div className="min-h-full bg-white flex flex-col">
      <div className="flex-1 flex flex-col justify-center py-6 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto w-full max-w-md lg:shadow-lg lg:p-8 rounded-sm">
          <Suspense fallback={<div>Loading...</div>}>
            <OtpForm />
          </Suspense>
        </div>
      </div>
    </div>
  );
};

export default OtpPage;
